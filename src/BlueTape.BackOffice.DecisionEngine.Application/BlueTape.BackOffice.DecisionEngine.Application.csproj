<Project Sdk="Microsoft.NET.Sdk">
    <ItemGroup>
      <ProjectReference Include="..\BlueTape.BackOffice.DecisionEngine.DataAccess.External\BlueTape.BackOffice.DecisionEngine.DataAccess.External.csproj" />
      <ProjectReference Include="..\BlueTape.BackOffice.DecisionEngine.Domain\BlueTape.BackOffice.DecisionEngine.Domain.csproj" />
    </ItemGroup>

    <ItemGroup>
      <PackageReference Include="AutoMapper" Version="12.0.1" />
      <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.1" />
      <PackageReference Include="bluetape.companyservice.common" Version="1.1.21" />
      <PackageReference Include="BlueTape.LS" Version="1.1.76" />
      <PackageReference Include="BlueTape.LS.Domain" Version="1.1.36" />
      <PackageReference Include="BlueTape.Notification.Sender" Version="1.0.2" />
      <PackageReference Include="BlueTape.Utilities" Version="1.4.6" />
      <PackageReference Include="bluetape.firebase" Version="1.0.1" />
      <PackageReference Include="BlueTape.AWSStepFunction" Version="1.0.4" />
      <PackageReference Include="BlueTape.Common.FileService" Version="1.0.6" />
    </ItemGroup>

</Project>
