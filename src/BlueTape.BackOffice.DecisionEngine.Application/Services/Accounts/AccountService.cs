using AutoMapper;
using BlueTape.BackOffice.DecisionEngine.Application.Constants;
using BlueTape.BackOffice.DecisionEngine.Application.Constants.DecisionEngine;
using BlueTape.BackOffice.DecisionEngine.Application.Extensions;
using BlueTape.BackOffice.DecisionEngine.Application.Services.Accounts.Constants;
using BlueTape.BackOffice.DecisionEngine.Application.Services.Accounts.Generators;
using BlueTape.BackOffice.DecisionEngine.Application.Services.Accounts.Models;
using BlueTape.BackOffice.DecisionEngine.Application.Services.Accounts.Models.List;
using BlueTape.BackOffice.DecisionEngine.Application.Services.Common.Generators;
using BlueTape.BackOffice.DecisionEngine.Application.Services.Companies.Models;
using BlueTape.BackOffice.DecisionEngine.Application.Services.CreditApplications.Generators;
using BlueTape.BackOffice.DecisionEngine.Application.Services.CreditApplications.Models;
using BlueTape.BackOffice.DecisionEngine.Application.Services.CreditApplications.Models.DocumentVerification;
using BlueTape.BackOffice.DecisionEngine.Application.Services.Reports;
using BlueTape.BackOffice.DecisionEngine.DataAccess.External.CompanyApi;
using BlueTape.BackOffice.DecisionEngine.DataAccess.External.LoanApi.Proxy;
using BlueTape.BackOffice.DecisionEngine.DataAccess.External.OnboardingApi;
using BlueTape.BackOffice.DecisionEngine.DataAccess.External.PlaidApi.Proxy;
using BlueTape.BackOffice.DecisionEngine.Domain.Enums;
using BlueTape.BackOffice.DecisionEngine.Domain.Enums.UI;
using BlueTape.BackOffice.DecisionEngine.Domain.Extensions;
using BlueTape.BackOffice.DecisionEngine.Domain.Models;
using BlueTape.Common.ExceptionHandling.Exceptions;
using BlueTape.CompanyService.BankAccounts;
using BlueTape.CompanyService.CashFlow.Queries;
using BlueTape.CompanyService.Common.Enums;
using BlueTape.CompanyService.Companies;
using BlueTape.CompanyService.Customers;
using BlueTape.LS.Domain.Enums;
using BlueTape.LS.DTOs.Credit;
using BlueTape.LS.DTOs.Loan;
using BlueTape.OBS.DTOs.CreditApplication;
using BlueTape.OBS.DTOs.CreditApplication.Queries;
using BlueTape.OBS.DTOs.CreditApplicationAuthorizationDetails;
using BlueTape.OBS.DTOs.DecisionEngineSteps;
using Microsoft.AspNetCore.Mvc;
using CompanyIdsDto = BlueTape.OBS.DTOs.CreditApplication.Queries.CompanyIdsDto;

namespace BlueTape.BackOffice.DecisionEngine.Application.Services.Accounts;

public class AccountService(
    IMapper mapper,
    ICompanyApiProxy companyApi,
    ILoanApiProxy loanApi,
    IOnboardingApiProxy onboardingApiProxy,
    IPlaidApiProxy plaidApiProxy,
    IReportFileGenerator reportsGeneratorService,
    IAccountCreditDetailsGenerator accountCreditDetailsGenerator)
    : IAccountsService
{

    public async Task<PaginatedList<AccountRecord>> GetApprovedAccounts(ApprovedAccountsFilter filter, CancellationToken ctx)
    {
        ArgumentNullException.ThrowIfNull(filter);
        filter.AccountStatus = filter.AccountStatus?.Length != 0 ? filter.AccountStatus : AccountFilters.ApprovedAccountStatuses;
        var creditAppFiltersProvided = filter.ProductType != null || filter.Category != null;
        var companyFilterProvided = filter.Id != null
                                    || filter.CompanyId != null
                                    || filter.Search != null
                                    || (filter.AccountStatus != null && filter.AccountStatus.Length != 0);
        var query = mapper.Map<CompanyQueryPaginated>(filter);

        if (creditAppFiltersProvided)
        {
            query.PageNumber = 1;
            query.PageSize = int.MaxValue;
        }
        var companies = await companyApi.GetCompaniesList(query, ctx);
        if (companies.Count == 0) return new PaginatedList<AccountRecord>();

        var paginatedCompaniesIds = companies.Result.Select(x => x.Id).Distinct().ToArray();

        var filteredCreditApplications = await FetchFilteredCreditApplications(filter, companyFilterProvided, paginatedCompaniesIds, ctx);

        if (creditAppFiltersProvided)
        {
            var filteredCompaniesIds = filteredCreditApplications.Select(x => x.CompanyId).Distinct().ToArray();
            var mappedCompanies = companies.Result.Where(company => filteredCompaniesIds.Contains(company.Id)).ToList();
            companies.Offset = filter.PageNumber;
            companies.Result = mappedCompanies
                .Skip(filter.PageSize * (filter.PageNumber - 1))
                .Take(filter.PageSize)
                .ToList();
            companies.Total = Math.Min(filteredCompaniesIds.Length, mappedCompanies.Count);
            companies.Count = (int)Math.Ceiling((decimal)companies.Total / filter.PageSize);
            paginatedCompaniesIds = companies.Result.Select(x => x.Id).Distinct().ToArray();
        }

        var creditList = (await loanApi.GetCreditsByCompanyIdArray(paginatedCompaniesIds, false, ctx)).ToList();
        var accountList = new List<AccountRecord>();
        foreach (var company in companies.Result)
        {
            var lineOfCredit = creditList.FirstOrDefault(c => c?.CompanyId == company.Id && c.Product == ProductType.LineOfCredit);
            var companyCreditApplications = filteredCreditApplications.Where(x => x.CompanyId == company.Id).ToArray();
            var account = MapAccount(lineOfCredit, companyCreditApplications, company);
            accountList.Add(account);
        }
        accountList = accountList.SortAccounts(filter.SortBy, filter.SortOrder).ToList();

        return new PaginatedList<AccountRecord>()
        {
            Result = accountList,
            PageNumber = companies.Offset ?? 0,
            PagesCount = companies.Count ?? 0,
            TotalCount = companies.Total ?? 0,
        };
    }

    private async Task<List<CreditApplicationDto>> FetchFilteredCreditApplications(ApprovedAccountsFilter filter,
        bool companyFilterProvided, string[] paginatedCompaniesIds, CancellationToken ctx)
    {
        var query = new GetCreditApplicationsByCompanyIdsQueryDto
        {
            Status = [nameof(OBS.Enums.CreditApplicationStatus.Approved)],
            Type = filter.ProductType?.Select(x => x.ToString()).ToArray(),
            Category = filter.Category?.Select(x => x.ToString()).ToArray()
        };

        var filteredCreditApplications = companyFilterProvided
            ? await onboardingApiProxy.GetCreditApplicationsByCompanyIds(
                new CompanyIdsDto { Ids = paginatedCompaniesIds }, query, ctx)
            : await onboardingApiProxy.GetCreditApplicationsList(
                mapper.Map<CreditApplicationQueryDto>(query), ctx);

        return filteredCreditApplications.ToList();
    }

    public async Task<PaginatedList<CashFlowItem>> GetAccountCashFlows(string companyId, CashFlowListQueryModel query, CancellationToken ct)
    {
        ArgumentNullException.ThrowIfNull(query);
        var result = await companyApi.GetPaginatedCashFlow(companyId, query.PageNumber, query.PageSize, ct);
        return mapper.Map<PaginatedList<CashFlowItem>>(result);
    }

    public async Task<AccountDetailedModel> GetDetailedAccount(string companyId, string? merchantId, UICreditApplicationType type, CancellationToken ct)
    {
        var company = (await companyApi.GetCompanyById(companyId, ct)).EnsureExists($"The company with id {companyId} was not found");
        var accAuth = await onboardingApiProxy.GetAccountAuthorizationByCompanyId(company.Id, ct) ?? null;
        var mappedType = type.UITypeToProductType();

        var creditAppTask = onboardingApiProxy.GetCreditApplicationsList(new CreditApplicationQueryPaginatedDto
        {
            CompanyId = companyId,
            Type = [type.UITypeToCreditApplicationType().ToString()],
            MerchantId = merchantId
        }, ct);
        var creditTask = GetCredits(companyId, merchantId, mappedType, ct);
        var ihcLoansTask = loanApi.GetLoansByQuery(new LoanQueryDto
        {
            EinHash = accAuth?.EinHash,
            Product = ProductType.InHouseCredit,
            Detailed = true
        }, ct);

        var bankAccountsTask = companyApi.GetBankAccountsByCompanyId(company.Id, ct);
        var companyDocumentsInfoRequest = companyApi.GetDocuments(company.Id, ct);
        var bankStatementsTask = companyApi.GetBankStatements(company.Id, ct);
        var customerAccounts = !string.IsNullOrEmpty(merchantId)
            ? companyApi.GetCustomersByMerchantIdAndCompanyId(merchantId, company.Id, ct)
            : Task.FromResult<IList<CustomerModel>>([]);

        await Task.WhenAll(creditTask, ihcLoansTask, bankAccountsTask, creditAppTask, bankStatementsTask, companyDocumentsInfoRequest, customerAccounts);

        var bankAccounts = await bankAccountsTask;
        var credits = await creditTask;
        var targetCreditApp = (await creditAppTask).MaxBy(x => x.CreatedAt);
        var bankStatements = mapper.Map<List<BankStatementDocument>>(await bankStatementsTask);
        var ihcLoans = await ihcLoansTask;
        var customerAccount = (await customerAccounts).FirstOrDefault();

        CompanyModel? merchantCompany = null;
        if (merchantId != null)
        {
            merchantCompany = await companyApi.GetCompanyById(merchantId, ct);
        }

        CreditDto credit = type switch
        {
            UICreditApplicationType.loc => credits.Where(x => x.Product == ProductType.LineOfCredit).MaxBy(d => d?.StartDate),
            UICreditApplicationType.ihc => credits.Where(x => x.Product == ProductType.InHouseCredit).MaxBy(d => d?.StartDate),
            UICreditApplicationType.aradvance => credits.Where(x => x.Product == ProductType.ARAdvance).MaxBy(d => d?.StartDate),
            _ => credits.MaxBy(d => d?.StartDate),
        };

        var companyDocumentsInfo = await companyDocumentsInfoRequest;
        CreditApplicationAuthorizationDetailsDto? authDetails = null;
        if (targetCreditApp is not null)
            authDetails = await onboardingApiProxy.GetApplicationAuthorizationDetails(targetCreditApp.Id, ct);

        var steps = (await onboardingApiProxy.GetDecisionStepsByQuery(new DecisionEngineStepQueryDto()
        {
            CompanyId = companyId,
        }, ct)).ToList();

        var documentVerificationResult = DocumentVerificationGenerator.Create(companyDocumentsInfo, authDetails, bankStatements);
        var step = steps.Where(x => x.ExecutionId == targetCreditApp?.ExecutionId).GetByName(DecisionSteps.BankAccountVerification);
        var automatedResult = step?.Status.ToEnum<AutomatedDecisionType>();

        return new AccountDetailedModel
        {
            Id = company.Id,
            CompanyId = company.Id,
            EinHash = accAuth?.EinHash ?? string.Empty,
            BusinessName = company.LegalName,
            Dba = targetCreditApp?.BusinessDba ?? string.Empty,
            Category = targetCreditApp?.BusinessCategory ?? string.Empty,
            AccountStatus = company.AccountStatus ?? AccountStatusEnum.New,
            Product = type.UITypeToCreditApplicationType(),
            AccountStatusText = company.StatusEvent ?? string.Empty,
            LastValidatedAt = steps.MaxBy(x => x.CreatedAt)?.CreatedAt ?? targetCreditApp?.CreatedAt ?? accAuth?.CreatedAt ?? DateTime.MinValue,
            CreditDetails = accountCreditDetailsGenerator.Create(credit),
            BankCreditStatus = BankAccountsGenerator.CreateCreditStatus(automatedResult, accAuth?.BusinessDetails, credit?.CreditDetails),
            BankAccounts = BankAccountsGenerator.CreateBankAccountDetails(accAuth, bankAccounts, steps),
            IhcOverAllDetails = IhcOverAllDetailsGenerator.Create(credits, ihcLoans),
            ManuallyUploadedDocuments = documentVerificationResult.ManuallyUploadedDocuments,
            DocumentApprovals = documentVerificationResult.EConsentLoCAgreement,
            BankStatements = documentVerificationResult.BankStatements,
            CustomerSettings = customerAccount?.Settings ?? (merchantCompany?.Settings != null
                ? new CustomerSettingsModel
                {
                    DebtInvestorTradeCredit = merchantCompany.Settings.DefaultDebtInvestorTradeCredit,
                    CalculatedDebtInvestorTradeCredit = merchantCompany.Settings.DefaultDebtInvestorTradeCredit,
                    InHouseCredit = merchantCompany.Settings.ARAdvance?.DefaultDebtInvestor is null ? null : new CustomerInHouseCreditModel
                    {
                        CalculatedDebtInvestor = merchantCompany.Settings.ARAdvance?.DefaultDebtInvestor
                    }
                }
                : new CustomerSettingsModel
                {
                    DebtInvestorTradeCredit = DebtInvestorType.Arcadia,
                    CalculatedDebtInvestorTradeCredit = DebtInvestorType.Arcadia,
                    InHouseCredit = new CustomerInHouseCreditModel
                    {
                        CalculatedDebtInvestor = DebtInvestorType.Arcadia
                    }
                }),
        };
    }

    private static AccountRecord MapAccount(CreditDto? lineOfCredit, CreditApplicationDto[] creditApplications, CompanyModel company)
    {
        var creditApp = creditApplications.FirstOrDefault();
        return new AccountRecord()
        {
            Id = company.Id,
            CompanyId = company.Id,
            CreditId = lineOfCredit?.Id ?? Guid.Empty,
            BusinessName = company.LegalName,
            AccountStatus = company.AccountStatus ?? AccountStatusEnum.New,
            CreatedAt = company.CreatedAt,
            AccountStatusText = company.StatusEvent ?? string.Empty,
            Products = creditApplications
                .Select(x => x.Type.ToString())
                .Distinct()
                .ToList(),
            Dba = creditApp?.BusinessDba ?? string.Empty,
            Category = creditApp?.BusinessCategory ?? string.Empty,
            EinHash = creditApp?.EinHash ?? string.Empty,
            ApplicantName = creditApp?.ApplicantName ?? string.Empty,
            ContactName = company.Owner?.ContactName ?? string.Empty,
            StatusEvent = lineOfCredit?.StatusEvent ?? string.Empty,
        };
    }

    public async Task<FileStreamResult> GetCompanyCashFlowTransactionsFile(string companyId, CancellationToken ctx)
    {
        var latestAssetReportRequest = (await plaidApiProxy.GetAssetRequestsByCompanyId(companyId, 1, true, ctx))?.FirstOrDefault()
                                       ?? throw new VariableNullException($"No asset report request found for the company {companyId}");
        var assetReport = (await plaidApiProxy.GetAssetReportByToken(latestAssetReportRequest.AssetReportToken, ctx))?.AssetReport;
        var company = await companyApi.GetCompanyById(companyId, ctx);

        var transactions = new List<CashFlowTransactionRecord>();
        if (assetReport == null || assetReport.Items.Count == 0)
        {
            return await ConvertTransactionsToFileStreamResult(company, transactions, ReportsConstants.CashFlowTransactionsReportName);
        }

        foreach (var account in assetReport.Items.SelectMany(item => item.Accounts))
        {
            transactions.AddRange(account.Transactions.Select(t => new CashFlowTransactionRecord()
            {
                Bank = $"{account.Name} ****{account.Mask}",
                AccountId = t.AccountId,
                Amount = t.Amount,
                Date = DateOnly.FromDateTime(t.Date.Date),
                IsoCurrencyCode = t.IsoCurrencyCode,
                OriginalDescription = t.OriginalDescription,
                Pending = t.Pending,
                TransactionId = t.TransactionId,
                UnofficialCurrencyCode = t.UnofficialCurrencyCode ?? string.Empty
            }));
        }

        return await ConvertTransactionsToFileStreamResult(company, transactions, ReportsConstants.CashFlowTransactionsReportName);
    }

    public async Task<FileStreamResult> GetCompanyCashFlowFile(string companyId, CancellationToken ctx)
    {
        var cashFlow = await companyApi.GetCashFlow(companyId, new AssetReportQuery(), ctx);
        var bankAccountIds = cashFlow.CashFlowItems.Select(x => x.AccountId)
            .Where(id => !string.IsNullOrEmpty(id))
            .Distinct()
            .ToArray();
        var bankAccounts = await companyApi.GetBankAccountsByAccountAndPlaidIds(bankAccountIds!, ctx);
        var company = await companyApi.GetCompanyById(companyId, ctx);
        var cashFlowRecords = new List<CashFlowRecord>();
        var accounts = new Dictionary<string, BankAccountModel?>();
        foreach (var cashFlowItem in cashFlow.CashFlowItems)
        {
            BankAccountModel? relatedAccount = null;
            if (!string.IsNullOrEmpty(cashFlowItem.AccountId))
            {
                var isRelatedAccountFound = accounts.TryGetValue(cashFlowItem.AccountId, out relatedAccount);
                if (!isRelatedAccountFound)
                {
                    relatedAccount = bankAccounts?.FirstOrDefault(x => x.Id == cashFlowItem.AccountId) ??
                                     bankAccounts?.FirstOrDefault(x => x.Plaid?.AccountId == cashFlowItem.AccountId);
                    accounts.Add(cashFlowItem.AccountId, relatedAccount);
                }
            }

            var cashFlowRecord = new CashFlowRecord()
            {
                AccountId = cashFlowItem.AccountId ?? string.Empty,
                Date = cashFlowItem.Date,
                Debit = cashFlowItem.Debit,
                Credit = cashFlowItem.Credit,
                Balance = cashFlowItem.Balance,
                AssetReportId = cashFlowItem.AssetReportId,
                CashFlow = cashFlowItem.CashFlowResult,
                AccountName = relatedAccount?.Name ?? string.Empty,
                AccountDisplay = relatedAccount?.AccountNumber?.Display ?? string.Empty
            };
            cashFlowRecords.Add(cashFlowRecord);
        }

        return await ConvertTransactionsToFileStreamResult(company, cashFlowRecords, ReportsConstants.CashFlowReportName);
    }

    private Task<FileStreamResult> ConvertTransactionsToFileStreamResult<T>(CompanyModel? company, List<T> records, string reportName)
    {
        var timestamp = reportsGeneratorService.GenerateReportTimeStamp();
        var fileName = $"{company?.Name}_{reportName}_{timestamp}";

        return reportsGeneratorService.GenerateReportFileStreamResult(records, ReportType.Xlsx, fileName);
    }

    private Task<IList<CreditDto?>> GetCredits(string companyId, string? merchantId, ProductType type, CancellationToken ct)
    {
        var filter = new CreditFilterDto()
        {
            CompanyId = companyId,
            Detailed = true
        };

        if (type == ProductType.InHouseCredit)
        {
            filter.Product = type;
            filter.MerchantId = merchantId;
        }

        return loanApi.GetCreditsByFilters(filter, ct);
    }
}
