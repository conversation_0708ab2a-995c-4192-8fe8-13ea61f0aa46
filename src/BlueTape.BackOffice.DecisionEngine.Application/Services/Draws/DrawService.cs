using BlueTape.BackOffice.DecisionEngine.Application.Extensions;
using BlueTape.BackOffice.DecisionEngine.Application.Services.Draws.Models;
using BlueTape.BackOffice.DecisionEngine.Application.Services.Reports;
using BlueTape.BackOffice.DecisionEngine.DataAccess.External.InvoiceApi;
using BlueTape.BackOffice.DecisionEngine.DataAccess.External.LoanApi.Proxy;
using BlueTape.BackOffice.DecisionEngine.DataAccess.External.OnboardingApi;
using BlueTape.BackOffice.DecisionEngine.Domain.Enums;
using BlueTape.BackOffice.DecisionEngine.Domain.Enums.UI;
using BlueTape.BackOffice.DecisionEngine.Domain.Models;
using BlueTape.Common.ExceptionHandling.Exceptions;
using BlueTape.LS.Domain.Enums;
using BlueTape.LS.Domain.Models;
using BlueTape.LS.DTOs.Loan;
using BlueTape.LS.DTOs.LoanPayables;
using BlueTape.OBS.DTOs.DrawApproval.Responses;
using BlueTape.OBS.Enums;
using BlueTape.Utilities.Extensions;
using Microsoft.AspNetCore.Mvc;

namespace BlueTape.BackOffice.DecisionEngine.Application.Services.Draws;

public class DrawService(
    ILoanApiProxy loanApiProxy,
    IOnboardingApiProxy onboardingApiProxy,
    IInvoiceApiProxy invoiceApiProxy,
    IReportFileGenerator reportFileGenerator) : IDrawService
{
    public async Task<PaginatedList<Draw>?> GetDrawsByQuery(PaginatedLoanQuery queryParams, CancellationToken ct)
    {
        ParseProductFilter(queryParams);
        ParseTypeFilter(queryParams);
        queryParams.Detailed = true;

        if (queryParams.HasUnPaidDownPayment == true && (queryParams.DownPaymentStatus is null || !queryParams.DownPaymentStatus.Any()))
        {
            queryParams.DownPaymentStatus = ["due", "expired"];
        }

        // Temporary local pagination before invoice number issue will be fixed
        var pageNumber = queryParams.PageNumber;
        var pageSize = queryParams.PageSize;
        var search = queryParams.Search;

        queryParams.PageNumber = 1;
        queryParams.PageSize = int.MaxValue;
        queryParams.Search = null;

        var draws = await loanApiProxy.GetDrawsByQuery(queryParams, ct);
        if (draws == null) return null;

        var payables = draws.Result.SelectMany(x => x?.LoanPayables ?? Enumerable.Empty<LoanPayablesDto>());

        // Always get draw approvals data
        var getDrawApprovalsDataRequest = GetDrawApprovalData(payables, ct);

        if (payables.Any(x => x.InvoiceNumber.IsNullOrEmpty()))
        {
            var getInvoiceDataRequest = GetInvoiceData(payables, ct);
            await Task.WhenAll(getInvoiceDataRequest, getDrawApprovalsDataRequest);
            await getInvoiceDataRequest;
        }
        else
        {
            // If we don't need invoice data, just wait for draw approvals
            await getDrawApprovalsDataRequest;
        }

        var drawApprovals = (await getDrawApprovalsDataRequest ?? Array.Empty<DrawApprovalDto?>()).ToList();

        // Temporary local pagination before invoice number issue will be fixed
        List<LoanDto>? filteredDraws = draws.Result;
        if (!search.IsNullOrEmpty())
        {
            filteredDraws = filteredDraws.Where(x =>
                !string.IsNullOrEmpty(x.MerchantName) && x.MerchantName.Contains(search!, StringComparison.OrdinalIgnoreCase)
                || x.LoanPayables.Any(payable => payable.InvoiceNumber != null
                                                 && payable.InvoiceNumber.Contains(search!, StringComparison.OrdinalIgnoreCase)))
                .ToList();
        }

        var total = filteredDraws.Count;
        var result = new PaginatedList<Draw>()
        {
            PagesCount = (int)Math.Round((double)total / pageSize, 0, MidpointRounding.ToPositiveInfinity),
            PageNumber = pageNumber,
            TotalCount = total,
            Result = filteredDraws
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .Select(draw => MapToDrawModel(draw, drawApprovals)).ToList()
        };

        /*
                var result = new PaginatedList<Draw>()
                {
                    PagesCount = draws.Count.GetValueOrDefault(0),
                    PageNumber = draws.Offset.GetValueOrDefault(0),
                    TotalCount = draws.Total.GetValueOrDefault(0),
                    Result = draws.Result.Select(draw => new Draw()
                    {
                        Id = draw.Id,
                        CreditId = draw.CreditId,
                        CompanyId = draw.CompanyId,
                        EinHash = draw.EinHash,
                        Type = draw.LoanOrigin.ParseLoanOriginToDrawType(),
                        DecisionDate = draw.StartDate,
                        MerchantName = draw.MerchantName,
                        Amount = draw.Amount,
                        NextPaymentAmount = draw.LoanDetails?.NextPaymentAmount ?? 0,
                        NextPaymentDate = draw.LoanDetails?.NextPaymentDate,
                        Status = draw.Status,
                        PayablesCount = draw.PayablesCount,
                        PayableAmount = draw.PayableAmount,
                        PayablesNumber = draw.PayablesNumber.IsNullOrEmpty() ? draw.LoanPayables?.FirstOrDefault()?.InvoiceNumber : draw.PayablesNumber,
                        DrawApprovalId = draw.DrawApprovalId,
                    }).ToList()
                };
                */
        return result;
    }

    public async Task<Draw> GetDrawById(Guid id, CancellationToken ct)
    {
        var draw = await loanApiProxy.GetDrawById(id, ct) ?? throw new VariableNullException(nameof(Draw));

        List<DrawApprovalDto?>? drawApprovals = null;

        // Always get draw approvals
        if (draw.LoanPayables?.Any() == true)
        {
            drawApprovals = (await GetDrawApprovalData(draw.LoanPayables, ct))?.ToList();

            // Only get invoice data if needed
            if (draw.LoanPayables.Any(x => x.InvoiceNumber.IsNullOrEmpty()))
            {
                await GetInvoiceData(draw.LoanPayables, ct);
            }
        }

        return MapToDrawModel(draw, drawApprovals);
    }

    public async Task<FileStreamResult> ExportDraws(PaginatedLoanQuery queryParams, CancellationToken ct)
    {
        queryParams.PageNumber = 1;
        queryParams.PageSize = int.MaxValue;
        queryParams.Detailed = true;

        if (queryParams.HasUnPaidDownPayment == true)
            queryParams.DownPaymentStatus = ["due", "expired"];

        var draws = await GetDrawsByQuery(queryParams, ct);
        if (draws == null || !draws.Result.Any())
        {
            return await reportFileGenerator.GenerateReportFileStreamResult(
                new List<DrawExportRecord>(), ReportType.Xlsx, GetExportFileName(queryParams));
        }

        var exportRecords = draws.Result.Select(MapToExportRecord).ToList();

        return await reportFileGenerator.GenerateReportFileStreamResult(
            exportRecords, ReportType.Xlsx, GetExportFileName(queryParams));
    }

    private static DrawExportRecord MapToExportRecord(Draw draw)
    {
        return new DrawExportRecord
        {
            Id = draw.Id.ToString(),
            LoanId = draw.CreditId?.ToString() ?? string.Empty,
            CompanyName = draw.CustomerName ?? string.Empty,
            SupplierName = draw.MerchantName ?? string.Empty,
            Amount = draw.Amount,
            PurchaseDate = draw.DecisionDate,
            Status = draw.Status.ToString(),
            Product = draw.Type == DrawType.NoSupplier ? "Direct Terms" : draw.Type?.ToString() ?? string.Empty,
            TemplateType = draw.Type == DrawType.NoSupplier ? "Direct Terms" : draw.Type?.ToString() ?? string.Empty,
            DueDate = draw.NextPaymentDate,
            DownPayment = draw.DownPaymentDetails?.Amount,
            DownPaymentStatus = draw.DownPaymentDetails?.Status.ToString() ?? string.Empty
        };
    }

    private static string GetExportFileName(PaginatedLoanQuery query)
    {
        return $"draws_export_{DateTime.Now:yyyy-MM-dd}";
    }

    private static void ParseTypeFilter(PaginatedLoanQuery queryParams)
    {
        if (queryParams.TemplateType == null || !queryParams.TemplateType.Any()) return;

        var mappings = queryParams.TemplateType
            .Select(x =>
            {
                if (Enum.TryParse<TemplateType>(x, true, out var templateType))
                {
                    return templateType switch
                    {
                        TemplateType.Regular => (TemplateType.Regular, LoanOrigin.Normal),
                        TemplateType.Express => (TemplateType.Regular, LoanOrigin.Express),
                        TemplateType.NoSupplier => (TemplateType.Regular, LoanOrigin.NoSupplier),
                        TemplateType.Quote => (null, LoanOrigin.Quote),
                        TemplateType.VirtualCard => (TemplateType.VirtualCard, null),
                        _ => (null, null)
                    };
                }
                return ((TemplateType?)null, (LoanOrigin?)null);
            })
            .Where(mapping => mapping.Item1 != null || mapping.Item2 != null)
            .ToList();

        queryParams.TemplateType = mappings.Where(x => x.Item1.HasValue).Select(x => x.Item1!.ToString()!).ToArray();
        queryParams.LoanOrigin = mappings.Where(x => x.Item2.HasValue).Select(x => x.Item2!.ToString()!).ToArray();
    }

    private async Task GetInvoiceData(IEnumerable<LoanPayablesDto> payables, CancellationToken ct)
    {
        if (!payables.Any()) return;

        var payablesWithoutNumbers = payables
            .Where(x => x.InvoiceNumber.IsNullOrEmpty());

        var invoices = await invoiceApiProxy.GetByIds(payablesWithoutNumbers.Select(x => x.PayableId).ToArray(), ct);

        if (invoices == null || !invoices.Any()) return;

        foreach (var payable in payablesWithoutNumbers)
        {
            payable.InvoiceNumber = invoices.FirstOrDefault(x => x.Id == payable.PayableId)?.InvoiceNumber;
        }
    }

    private async Task<IEnumerable<DrawApprovalDto?>?> GetDrawApprovalData(IEnumerable<LoanPayablesDto> payables, CancellationToken ct)
    {
        if (!payables.Any()) return null;

        var payablesIds = payables.Select(x => x.PayableId).ToArray();

        var drawApprovals = await onboardingApiProxy.GetDrawApprovalsByInvoicesIds(payablesIds, ct);

        return drawApprovals;
    }

    private static void ParseProductFilter(PaginatedLoanQuery queryParams)
    {
        var creditAppType = queryParams.Product.ParseToEnum<UICreditApplicationType>();
        if (creditAppType == null) return;

        var product = creditAppType.Value.UITypeToCreditApplicationType();
        queryParams.Product = product.ToString();
    }

    private static Draw MapToDrawModel(LoanDto draw, List<DrawApprovalDto?>? drawApprovals)
    {
        var loanParameters = draw?.LoanParameters?.FirstOrDefault(x => x.IsActive);
        var originalDownPaymentAmount = loanParameters?.DownPaymentAmount;
        var drawProcessingAndPaidAmount = draw?.LoanDetails?.TotalPaid + draw?.LoanDetails?.TotalProcessingPaymentsAmount;
        var downPaymentOutstandingAmount = draw?.DownPaymentStatus == DownPaymentStatus.Paid ?
            0 : drawProcessingAndPaidAmount >= originalDownPaymentAmount ?
                0 : originalDownPaymentAmount - drawProcessingAndPaidAmount;

        DrawApprovalDto? matchingDrawApproval = null;
        if (drawApprovals != null)
        {
            matchingDrawApproval = drawApprovals.FirstOrDefault(x => x?.Id == draw?.DrawApprovalId);
        }

        return new Draw()
        {
            Id = draw.Id,
            CreditId = draw.CreditId,
            CompanyId = draw.CompanyId,
            CustomerName = draw.CompanyName?.Contains(" / ") == true 
                ? draw.CompanyName.Split(" / ")[0].Trim() 
                : draw.CompanyName,
            Dba = draw.CompanyName?.Contains(" / ") == true 
                ? draw.CompanyName.Split(" / ")[1].Trim() 
                : draw.CompanyName,
            EinHash = draw.EinHash,
            Type = draw.ParseDrawType(),
            DecisionDate = draw.StartDate,
            MerchantName = draw.MerchantName,
            Amount = draw.Amount,
            NextPaymentAmount = draw.LoanDetails?.NextPaymentAmount ?? 0,
            NextPaymentDate = draw.LoanDetails?.NextPaymentDate,
            Status = draw.Status,
            PayablesCount = draw.PayablesCount,
            PayableAmount = draw.PayableAmount,
            PayablesNumber = draw.PayablesNumber.IsNullOrEmpty() ? draw.LoanPayables?.FirstOrDefault()?.InvoiceNumber : draw.PayablesNumber,
            DrawApprovalId = draw.DrawApprovalId,
            LoanOutstandingAmount = draw.LoanDetails?.LoanOutstandingAmount ?? 0,
            DownPaymentDetails = new DownPaymentDetails()
            {
                Amount = downPaymentOutstandingAmount,
                ExpireAt = loanParameters?.DownPaymentExpireAt,
                Status = draw.DownPaymentStatus,
                StatusAt = draw.DownPaymentStatusAt,
                Percentage = loanParameters?.DownPaymentPercentage,
                PaymentMethod = matchingDrawApproval?.DownPaymentDetails?.PaymentMethod,
                AccountId = matchingDrawApproval?.DownPaymentDetails?.AccountId,
            }
        };
    }
}
