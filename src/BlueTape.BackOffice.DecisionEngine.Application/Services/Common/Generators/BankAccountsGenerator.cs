using BlueTape.BackOffice.DecisionEngine.Application.Constants.DecisionEngine;
using BlueTape.BackOffice.DecisionEngine.Application.Extensions;
using BlueTape.BackOffice.DecisionEngine.Application.Services.Common.Models;
using BlueTape.BackOffice.DecisionEngine.Application.Services.CreditApplications.Models;
using BlueTape.BackOffice.DecisionEngine.Application.Services.CreditApplications.Models.DecisionEngineExecutions;
using BlueTape.BackOffice.DecisionEngine.Application.Services.DrawApprovals.Generators;
using BlueTape.BackOffice.DecisionEngine.Domain.Enums;
using BlueTape.CompanyService.BankAccounts;
using BlueTape.LS.DTOs.Credit;
using BlueTape.LS.DTOs.Credit.CreditDetails;
using BlueTape.OBS.DTOs.AccountAuthorization;
using BlueTape.OBS.DTOs.CreditApplicationAuthorizationDetails;
using BlueTape.OBS.DTOs.DecisionEngineSteps;
using System.Text.RegularExpressions;
using BankAccountGiactModel = BlueTape.BackOffice.DecisionEngine.Application.Services.CreditApplications.Models.BankAccountGiactModel;

namespace BlueTape.BackOffice.DecisionEngine.Application.Services.Common.Generators;

public static class BankAccountsGenerator
{
    public static DeExecutionBankDetails Create(CreditApplicationAuthorizationDetailsDto? authDetails, AccountAuthorizationDto? accAuth, IList<BankAccountModel>? accounts, IList<DecisionEngineStepsDto> steps, IList<CashFlowItem> cashFlowItems, CreditDto? credit)
    {
        var step = steps.GetByName(DecisionSteps.BankAccountVerification);
        var bankStatementValidationStep = steps.GetByName(DecisionSteps.BankStatementValidation);
        var cashFlowReviewStep = steps.GetByName(DecisionSteps.CashFlowReview);
        var automatedResult = step?.Status == DecisionSteps.Skipped
            ? bankStatementValidationStep?.Status.ToEnum<AutomatedDecisionType>()
            : step?.Status.ToEnum<AutomatedDecisionType>();

        var businessDetails = authDetails?.AccountAuthorizationDetailsSnapshot?.BusinessDetails ?? new();
        businessDetails.DebtByCustomer ??= accAuth?.BusinessDetails.DebtByCustomer;
        businessDetails.RevenueByCustomer ??= accAuth?.BusinessDetails.RevenueByCustomer;
        businessDetails.DebtAdjustor ??= accAuth?.BusinessDetails.DebtAdjustor ?? 1.2m;
        businessDetails.AcceptablePercentRevenue ??= accAuth?.BusinessDetails.AcceptablePercentRevenue ?? 15.0m;
        businessDetails.AnnualRevenue ??= accAuth?.BusinessDetails.AnnualRevenue;

        var result = new DeExecutionBankDetails
        {
            AnnualRevenue = cashFlowReviewStep.CreateResultDetails(businessDetails.AnnualRevenue, DecisionSources.CashFlowReview.CashFlowAnnualRevenueRuleResult),
            Result = automatedResult,
            CashFlowItems = cashFlowItems,
            BankCreditStatus = CreateCreditStatus(automatedResult, businessDetails, credit?.CreditDetails)
        };

        if (accounts is { Count: > 0 })
        {
            foreach (var details in accounts)
            {
                if (details.Plaid != null)
                    result.BankAccounts.Add(CreatePlaidAccount(details, step));
                if (details.IsManualEntry == true)
                    result.BankAccounts.Add(CreateManualAccount(details));
                if (details.Giact != null || details.IsManualEntry == true)
                    result.Giact.Add(CreateGiactResult(details, bankStatementValidationStep));
            }
        }

        return result;
    }

    public static BankCreditStatus CreateCreditStatus(AutomatedDecisionType? automatedResult, BusinessDetailsDto? businessDetails, CreditDetailsDto? creditDetails)
    {
        var creditStatus = new BankCreditStatus()
        {
            DebtAdjustor = businessDetails?.DebtAdjustor,
            AcceptablePercentRevenue = businessDetails?.AcceptablePercentRevenue,
            RevenueByCustomer = businessDetails?.RevenueByCustomer,
            DebtByCustomer = businessDetails?.DebtByCustomer,
        };
        if (automatedResult != AutomatedDecisionType.HardFail)
        {
            creditStatus.BusinessOutstandingBalance = creditDetails?.OutstandingCredit;
            creditStatus.AnnualRevenue = businessDetails?.AnnualRevenue;
            creditStatus.CompanyIncome = businessDetails?.CompanyIncome;
            creditStatus.CurrentDebt = businessDetails?.CurrentDebt;
            creditStatus.LoanDebt = businessDetails?.LoanDebt;
            creditStatus.TotalAcceptableDebtAmount = businessDetails?.TotalAcceptableDebtAmount;
            creditStatus.AvailableCreditLimit = businessDetails?.AvailableCreditLimit;
        }
        return creditStatus;
    }

    public static DeExecutionBankDetails CreateDeExecution(CreditApplicationAuthorizationDetailsDto? authDetails, AccountAuthorizationDto? accAuth, IList<BankAccountModel>? accounts, IList<DecisionEngineStepsDto> steps, IList<CashFlowItem> cashFlowItems, CreditDto? credit)
    {
        var result = Create(authDetails, accAuth, accounts, steps, cashFlowItems, credit);
        var latest = steps.GetLatestByNames(DecisionSteps.BankAccountVerification, DecisionSteps.BankStatementValidation);
        return result.FillDeExecutionBaseFields(steps, latest?.Step);
    }

    public static IList<BankAccountDetails> CreateBankAccountDetails(AccountAuthorizationDto? authDetails, IList<BankAccountModel>? accounts, IList<DecisionEngineStepsDto> steps)
    {
        var step = steps.GetByName(DecisionSteps.BankAccountVerification);

        var result = new List<BankAccountDetails>();

        if (accounts == null || accounts.Count == 0 || authDetails?.BankAccountDetails == null)
            return result;

        foreach (var account in authDetails.BankAccountDetails)
        {
            var details = accounts.FirstOrDefault(a => a.Id == account.Id);
            var data = CreatePlaidAccount(details, step);
            result.Add(data);
        }

        return result;
    }

    private static BankAccountDetails CreatePlaidAccount(BankAccountModel? details, DecisionEngineStepsDto? step)
    {
        var checkingAccount = step?
            .Results?
            .FirstOrDefault(r =>
                r.ComparisonSource == DecisionSources.BankAccounts.CheckingAccountExists &&
                r.BankAccountIdentifier == details?.Id)?.Result;

        var checkingAccountValue = step?
            .Results?
            .FirstOrDefault(r =>
                r.ComparisonSource == DecisionSources.BankAccounts.CheckingAccountExists &&
                r.BankAccountIdentifier == details?.Id)?.ComparisonValue;

        var businessNameVerification = step?
            .Results?
            .FirstOrDefault(r =>
                r.ComparisonSource == DecisionSources.BankAccounts.BankAccountNameAndAddress &&
                r.BankAccountIdentifier == details?.Id &&
                r.Result != DecisionSteps.Pass)?.Result ?? (step is null ? null : DecisionSteps.Pass);

        var isIncludeInCashFlow = details?.Plaid?.IncludeInCashFlow != null && Convert.ToBoolean(details?.Plaid?.IncludeInCashFlow);

        return new BankAccountDetails
        {
            Identifier = details?.Id,
            Name = CreateBankAccountName(details),
            IsCheckingAccount = new CheckResult<bool>()
            {
                Result = checkingAccount.ToEnum<AutomatedDecisionType>(),
                Value = checkingAccountValue != null && checkingAccountValue.ToString() == DecisionSources.BankAccounts.Checking
            },
            BusinessNameVerification = businessNameVerification.ToEnum<AutomatedDecisionType>(),
            IncludeInCashFlow = isIncludeInCashFlow,
            PlaidConnectionStatus = details?.Plaid?.Status.ToEnum<PlaidConnectionStatus>(),
            ConnectionType = BankAccountConnectionType.Plaid,
            AccountHolderName = details?.AccountHolderName,
            AccountNumber = details?.AccountNumber?.Display,
            RoutingNumber = details?.RoutingNumber,
            IsPrimary = details?.IsPrimary,
        };
    }

    private static BankAccountDetails CreateManualAccount(BankAccountModel? details)
    {
        return new BankAccountDetails
        {
            Identifier = details?.Id,
            Name = CreateBankAccountName(details),
            IsCheckingAccount = new CheckResult<bool>
            {
                Result = null
            },
            BusinessNameVerification = null,
            IncludeInCashFlow = details?.IncludeInCashFlow ?? true,
            PlaidConnectionStatus = null, // not exists for manual uploaded bank accounts
            ConnectionType = BankAccountConnectionType.Manual,
            AccountHolderName = details?.AccountHolderName,
            AccountNumber = details?.AccountNumber?.Display,
            RoutingNumber = details?.RoutingNumber,
            IsPrimary = details?.IsPrimary,
        };
    }

    private static string CreateBankAccountName(BankAccountModel? bankAccount)
    {
        if (bankAccount is null) return string.Empty;

        const string regex = @"^(.*?)(\s*/\s*.*?)?(?:\s*\.\.\.\d{4})?$";
        var displayedAccountNumber = $"********{bankAccount.AccountNumber!.Display![^4..]}";
        var nameToProcess = string.IsNullOrEmpty(bankAccount.AccountName) ? bankAccount.Name : bankAccount.AccountName;

        if (nameToProcess == null)
            return string.Empty;
        
        var cleanedNameToProcess = Regex.Replace(nameToProcess!, @"\s*\.\.\.\d{4}$", "", RegexOptions.None, TimeSpan.FromMilliseconds(100));

        var match = Regex.Match(cleanedNameToProcess, regex, RegexOptions.None, TimeSpan.FromMilliseconds(100));

        string result;
        if (match.Success)
        {
            result = string.IsNullOrWhiteSpace(match.Groups[2].Value)
                ? match.Groups[1].Value
                : match.Groups[1].Value + match.Groups[2].Value;
        }
        else
        {
            result = cleanedNameToProcess;
        }

        result = Regex.Replace(result, @"\s*/\s*$", "", RegexOptions.None, TimeSpan.FromMilliseconds(100));
        return result.TrimEnd() + " " + displayedAccountNumber;
    }

    private static BankAccountGiactModel CreateGiactResult(BankAccountModel? bankAccount, DecisionEngineStepsDto? step)
    {
        return new BankAccountGiactModel
        {
            Identifier = bankAccount!.Id,
            Name = CreateBankAccountName(bankAccount),
            IsVerified = step is not null ? bankAccount.Giact?.Status == DecisionSources.BankAccounts.GiactVerifiedStatus : null,
            AccountHolderName = bankAccount.AccountHolderName,
            AccountNumber = bankAccount.AccountNumber?.Display,
            RoutingNumber = bankAccount.RoutingNumber
        };
    }
}
