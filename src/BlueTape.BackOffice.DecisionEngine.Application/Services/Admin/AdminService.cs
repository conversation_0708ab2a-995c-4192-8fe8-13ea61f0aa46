using Amazon.S3.Util;
using AutoMapper;
using BlueTape.AWSS3.Abstractions;
using BlueTape.BackOffice.DecisionEngine.Application.Constants.DecisionEngine;
using BlueTape.BackOffice.DecisionEngine.Application.Extensions;
using BlueTape.BackOffice.DecisionEngine.Application.Extensions.Models;
using BlueTape.BackOffice.DecisionEngine.Application.Services.Accounts.Models;
using BlueTape.BackOffice.DecisionEngine.Application.Services.Admin.Models;
using BlueTape.BackOffice.DecisionEngine.Application.Services.BankAccounts;
using BlueTape.BackOffice.DecisionEngine.Application.Services.DrawApprovals.Models;
using BlueTape.BackOffice.DecisionEngine.Application.Services.DrawApprovals.Models.Details;
using BlueTape.BackOffice.DecisionEngine.Application.Services.DrawApprovals.Models.List;
using BlueTape.BackOffice.DecisionEngine.DataAccess.External.CompanyApi;
using BlueTape.BackOffice.DecisionEngine.DataAccess.External.InvoiceApi;
using BlueTape.BackOffice.DecisionEngine.DataAccess.External.LoanApi.Proxy;
using BlueTape.BackOffice.DecisionEngine.DataAccess.External.OnboardingApi;
using BlueTape.BackOffice.DecisionEngine.DataAccess.External.PaymentApi.Proxy;
using BlueTape.BackOffice.DecisionEngine.Domain.Enums;
using BlueTape.BackOffice.DecisionEngine.Domain.Exceptions;
using BlueTape.BackOffice.DecisionEngine.Domain.Extensions;
using BlueTape.BackOffice.DecisionEngine.Domain.Models;
using BlueTape.Common.Extensions.Abstractions;
using BlueTape.CompanyService.BankAccounts;
using BlueTape.CompanyService.BankAccounts.Giact;
using BlueTape.CompanyService.CashFlow;
using BlueTape.CompanyService.CashFlow.Responses;
using BlueTape.CompanyService.Common.Enums;
using BlueTape.CompanyService.Companies;
using BlueTape.InvoiceService.Common.Enums;
using BlueTape.InvoiceService.Models.Company;
using BlueTape.InvoiceService.Models.Invoice;
using BlueTape.LS.Domain.Enums;
using BlueTape.LS.DTOs.CardPricingPackages;
using BlueTape.LS.DTOs.Credit;
using BlueTape.LS.DTOs.Enums;
using BlueTape.LS.DTOs.Loan;
using BlueTape.LS.DTOs.LoanPricingPackages;
using BlueTape.Notification.Sender.Abstractions;
using BlueTape.Notification.Sender.Enums;
using BlueTape.Notification.Sender.SystemNotifications;
using BlueTape.OBS.DTOs.CreditApplication;
using BlueTape.OBS.DTOs.CreditApplication.Requests;
using BlueTape.OBS.DTOs.DecisionEngineSteps;
using BlueTape.OBS.DTOs.DrawApproval.Requests;
using BlueTape.OBS.DTOs.DrawApprovalNotes;
using BlueTape.OBS.Enums;
using BlueTape.PaymentService.Enums;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using System.Net;
using System.Reflection;
using System.Runtime.Serialization;
using System.Text.Json;
using AddressModel = BlueTape.CompanyService.Companies.AddressModel;
using CompanyModel = BlueTape.CompanyService.Companies.CompanyModel;
using CreditApplicationStatus = BlueTape.OBS.Enums.CreditApplicationStatus;
using DebtInvestorType = BlueTape.CompanyService.Common.Enums.DebtInvestorType;

namespace BlueTape.BackOffice.DecisionEngine.Application.Services.Admin;

public class AdminService(
    ILoanApiProxy loanApi,
    ICompanyApiProxy companyApi,
    IS3Client s3Client,
    IConfiguration configuration,
    IOnboardingApiProxy onboardingApiProxy,
    IInvoiceApiProxy invoiceApi,
    IPaymentApiProxy paymentApi,
    IBankAccountDecryptor accountDecryptor,
    IMapper mapper,
    IAzureNotificationSenderService azureNotificationSenderService,
    ITraceIdAccessor traceIdAccessor)
    : IAdminService
{
    public async Task<CreditDto> UpdateCreditDetails(Guid id, string userId, UpdateCreditDetailsDto updateCreditDto, CancellationToken ct)
    {
        var currentCredit = await loanApi.GetCreditById(id, false, ct);
        var result = await loanApi.UpdateCreditDetails(id, userId, updateCreditDto, ct);
        try
        {
            await onboardingApiProxy.PatchAdmin(result.CreditApplicationId, new PatchCreditApplicationAdminDto
            {
                ApprovedCreditLimit = result.CreditLimit,
                PurchaseTypeOption = result.PurchaseType.ToString() ?? null,
                RevenueFallPercentage = result.RevenueFallPercentage
            }, ct);
        }
        catch (Exception ex)
        {
            await loanApi.UpdateCreditDetails(id, userId, new UpdateCreditDetailsDto
            {
                CreditLimit = currentCredit!.CreditLimit,
                PurchaseType = currentCredit.PurchaseType,
                RevenueFallPercentage = currentCredit.RevenueFallPercentage,
                Notes = "Error was failed during credit patching, updating the data to original state"
            }, ct);

            throw new InvalidOperationException($"Unable to update Credit {currentCredit.Id} and CreditApplication. Message: {ex.Message}");
        }

        return result;
    }

    public Task<StepFunctionsExecutionResponse> RunGetPaidApplicationAsArAdvance(string getPaidApplicationId, CancellationToken ct)
    {
        return onboardingApiProxy.RunGetPaidApplicationAsArAdvance(getPaidApplicationId, ct);
    }

    public async Task<AddManualCashFlow> AddManualCashFlow(string companyId, string accountId, string userId, IFormFile cashFlowReportFile, CancellationToken ctx)
    {
        var result = await companyApi.AddManualCashFlow(companyId, accountId, userId, cashFlowReportFile, ctx);

        var creditApplications = await onboardingApiProxy.GetCreditApplicationsByCompanyId(companyId, ctx);
        var creditApp = creditApplications.MaxBy(x => x.CreatedAt) ?? null;

        if (string.Equals(creditApp?.Status, CreditApplicationStatus.Approved.ToString(), StringComparison.InvariantCultureIgnoreCase))
            return result;

        await onboardingApiProxy.StartCreditApplicationInitializationStep(new CreditApplicationInitializationStepStartRequest()
        {
            CreditApplicationId = creditApp?.Id ?? string.Empty,
            MerchantId = creditApp?.MerchantId,
            Type = creditApp?.Type.ToUIType().ToString()
        }, ctx);

        return result;

    }

    public async Task<CompanyModel?> PatchDefaultDebtInvestorTradeCredit(string userId, string id, DebtInvestorType newDefaultDebtInvestorTradeCredit, CancellationToken ctx)
    {
        var newCompanyModel = new UpdateCompanyModel
        {
            Settings = new()
            {
                DefaultDebtInvestorTradeCredit = newDefaultDebtInvestorTradeCredit
            }
        };

        var result = await companyApi.UpdateCompany(id, userId, newCompanyModel, ctx);

        return result;
    }

    public async Task<ManualCashFlowValidationResultResponse> ValidateManualCashFlow(IFormFile cashFlowReportFile, CancellationToken ctx)
    {
        var result = await companyApi.ValidateManualCashFlow(cashFlowReportFile, ctx);

        return result;
    }

    public async Task RunDecisionEngineForCreditApplication(string creditApplicationId, CancellationToken ctx)
    {
        var creditApp = await onboardingApiProxy.GetCreditApplication(creditApplicationId, ctx);

        await onboardingApiProxy.StartCreditApplicationInitializationStep(new CreditApplicationInitializationStepStartRequest()
        {
            CreditApplicationId = creditApplicationId,
            MerchantId = creditApp?.MerchantId,
            Type = creditApp?.Type.ToUIType().ToString()
        }, ctx);

        try
        {
            await RetriesExtensions.ExecuteWithRetriesAsync(async () =>
            {
                var creditApplication = await onboardingApiProxy.GetCreditApplication(creditApplicationId, ctx);
                if (string.Equals(creditApplication?.Status, CreditApplicationStatus.Processing.ToString(), StringComparison.InvariantCultureIgnoreCase))
                {
                    return creditApplication;
                }

                return null;
            }, new RetryConfiguration()
            {
                RetriesCount = 4
            });
        }
        catch (TimeoutException) { }
    }

    public async Task RunDecisionEngineForDrawApproval(string drawApprovalId, CancellationToken ctx)
    {
        var existingDrawApproval = (await onboardingApiProxy.GetDrawApproval(drawApprovalId, ctx)).EnsureExists($"DrawApproval with Id: {drawApprovalId} was not found.");
        var creditId = existingDrawApproval.CreditId;
        if (existingDrawApproval.Type is not DrawApprovalType.Factoring && !Guid.TryParse(creditId, out _))
        {
            creditId = (await loanApi.GetCreditsByFilters(new CreditFilterDto()
            {
                CompanyId = existingDrawApproval.CompanyId,
                Product = ProductType.LineOfCredit,
                Status = LS.Domain.Enums.CreditStatus.Active
            }, ctx)).FirstOrDefault()?.Id.ToString();
        }

        await onboardingApiProxy.StartDrawApprovalInitializationStep(new DrawApprovalInitializationStepStartRequest()
        {
            DrawApprovalId = drawApprovalId,
            CreditId = creditId
        }, ctx);

        try
        {
            await RetriesExtensions.ExecuteWithRetriesAsync(async () =>
            {
                var drawApproval = await onboardingApiProxy.GetDrawApproval(drawApprovalId, ctx);
                if (drawApproval?.Status is DrawApprovalStatus.Processing)
                {
                    return drawApproval;
                }

                return null;
            }, new RetryConfiguration()
            {
                RetriesCount = 4
            });
        }
        catch (TimeoutException) { }
    }

    public async Task CancelDraw(Guid drawId, string userId, CancellationToken ct)
    {
        try
        {
            var draw = await loanApi.GetDrawById(drawId, ct);
            if (draw == null)
            {
                throw new ValidationException($"Draw with Id: {drawId} was not found.", HttpStatusCode.NotFound);
            }

            if (draw.Status != LoanStatus.Canceled)
            {
                if (draw.Status != LoanStatus.Created)
                {
                    throw new ValidationException($"Draw with Id: {drawId} has '{draw.Status}' status. Only Draws with the 'Created' status can be cancelled.");
                }

                if (draw.Payments is not null && draw.Payments.Count > 0)
                {
                    throw new ValidationException($"Draw with Id: {drawId} has payments. Only Draws without any payments can be cancelled.");
                }

                var filterQuery = new PaymentRequestFilterQuery
                {
                    DrawId = drawId.ToString(),
                    RequestType = PaymentRequestType.DrawDisbursement,
                    PageNumber = 1,
                    PageSize = int.MaxValue,
                };
                var disbursements = await paymentApi.GetPaymentRequests(filterQuery, ct);
                if (disbursements is not null && disbursements.Result.Any(p => p.Status != PaymentRequestStatus.Requested || p.ConfirmedAt is not null))
                {
                    throw new ValidationException($"Draw with Id: {drawId} has confirmed disbursements. Only Draws without confirmed disbursements can be cancelled.");
                }

                var updateLoanDetails = new UpdateLoanDto
                {
                    Status = InputLoanStatus.Canceled
                };
                await loanApi.ChangeLoanStatus(drawId, userId, updateLoanDetails, ct);
            }

            if (!string.IsNullOrEmpty(draw.DrawApprovalId))
            {
                var patchDrawApprovalDetails = new PatchDrawApprovalInternalRequest
                {
                    Status = DrawApprovalStatus.Canceled,
                    UpdatedBy = userId,
                    UpdatedAt = DateTime.UtcNow
                };
                await onboardingApiProxy.PatchDrawApproval(draw.DrawApprovalId, patchDrawApprovalDetails, ct);
            }

            if (draw.LoanPayables != null && draw.LoanPayables.Any())
            {
                var patchInvoiceStatus = new PatchInvoiceStatusModel
                {
                    Status = InvoiceStatus.CANCELLED,
                };

                foreach (var invoice in draw.LoanPayables)
                {
                    await invoiceApi.PatchInvoiceStatusAsync(invoice.PayableId, patchInvoiceStatus, ct);
                }
            }

            await paymentApi.CancelPaymentByDrawId(drawId, userId, ct);

            if (!string.IsNullOrEmpty(draw.DrawApprovalId))
            {
                var patchDrawApprovalDetails = new CreateDrawApprovalNoteDto
                {
                    Note = "Draw has canceled due to missing down payment."
                };
                await onboardingApiProxy.AddDrawApprovalNote(draw.DrawApprovalId, patchDrawApprovalDetails, ct);
            }

            if (string.IsNullOrEmpty(draw.CompanyId))
            {
                throw new ValidationException($"No CompanyId found for draw with Id: {drawId}.", HttpStatusCode.PreconditionRequired);
            }

            var activeUsers = await companyApi.GetCompanyActiveUsers(draw.CompanyId!, ct);

            if (activeUsers == null || !activeUsers.Any())
            {
                throw new ValidationException($"No active users found for company with Id: {draw.CompanyId}.", HttpStatusCode.PreconditionRequired);
            }

            const string cancelDrawNotificationTemplateId = "d-2a5359edd2a74c00b88383339ea732a4";

            var dynamicEmailData = new
            {
                customerName = GetCustomerName(activeUsers)
            };

            var notificationsReferenceIds = draw.LoanPayables?.Select(x => x.PayableId).ToList() ?? new();
            notificationsReferenceIds.Add(draw.Id.ToString());

            var drawCancellationNotification = new SystemNotificationDto
            {
                Source = NotificationSource.BFF,
                TraceId = traceIdAccessor.TraceId,
                CompanyId = draw.CompanyId,
                NotificationName = "DrawCancellationDueUnpaidDownPayment",
                Description = $"Cancel Draw with Id {drawId}",
                ReferenceIds = notificationsReferenceIds,
                EmailDelivery = new List<NotificationChannelDto<EmailPayloadDto>>
                {
                    new()
                    {
                        Payload = new EmailPayloadDto
                        {
                            Html = string.Empty,
                            TemplateId = cancelDrawNotificationTemplateId,
                            TemplatePayload = JsonSerializer.Serialize(dynamicEmailData),
                            Subject = "Draw has been cancelled due to unpaid Down Payment",
                            From = new EmailReceiverDataDto()
                            {
                                Email = "<EMAIL>",
                                Name = "BlueTape Inc"
                            },

                            Receivers = activeUsers.Where(user => !string.IsNullOrEmpty(user.Email))
                                .Select(user => new EmailReceiverDataDto()
                                {
                                    Email = user.Email!,
                                    Name = string.Empty
                                }).ToList(),
                        }
                    }
                },
                UserUiReviewDelivery = new List<NotificationChannelDto<UserUiReviewPayloadDto>>(),
                BlueTapeBackOfficeDelivery = new List<NotificationChannelDto<BlueTapeBackOfficePayloadDto>>()
            };

            await azureNotificationSenderService.Send(drawCancellationNotification, ct);
        }
        catch (ValidationException ex)
        {
            throw;
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"Unable to cancel Draw with Id {drawId}. Message: {ex.Message}");
        }
    }

    public async ValueTask<Uri> GetAWSPreSignedUrl(string bucketReference, CancellationToken ctx)
    {
        var isAbsolutePath = Uri.TryCreate(bucketReference, UriKind.Absolute, out var uri);
        if (isAbsolutePath)
        {
            var isS3Uri = AmazonS3Uri.TryParseAmazonS3Uri(bucketReference, true, out var S3Uri);
            if (isS3Uri)
            {
                var bucket = S3Uri.Bucket;
                var bucketKey = S3Uri.Key;
                var result = s3Client.GetPreSignedUrl(bucketKey, bucket, TimeSpan.FromMinutes(2));
                return new Uri(result, UriKind.Absolute);
            }
        }

        if (isAbsolutePath) return uri!;

        string bucketURI = configuration["S3Options:UserAssetsBucket"] ?? string.Empty;
        var isFileInS3Bucket = await s3Client.IsFileExistInS3BucketAsync(bucketURI, bucketReference, ctx);

        if (isFileInS3Bucket)
        {
            var result = s3Client.GetPreSignedUrl(bucketReference, bucketURI, TimeSpan.FromMinutes(2));
            return new Uri(result, UriKind.Absolute);
        }

        throw new FileNotFoundException($"The file {bucketReference} at bucket {bucketURI} was not found");
    }

    public Task PatchCashFlowInclusion(string bankAccountId, PatchBankAccountCashFlowModel model, CancellationToken ct)
    {
        ArgumentNullException.ThrowIfNull(model);
        return companyApi.PatchCashFlowInclusion(bankAccountId, model.IncludeInCashFlow, ct);
    }

    public async Task<BankAccountModel?> UpdateBankAccountNumberModel(string bankAccountId, string userId,
        UpdateBankAccountNumberRequestModel dto, CancellationToken ct)
    {
        ArgumentNullException.ThrowIfNull(dto);
        UpdateBankAccountNumberModel model = new()
        {
            GiactVerificationResult = dto.GiactVerificationResult
        };
        if (dto.BankDetails != null)
        {
            var accountNumberEncrypted = await accountDecryptor.EncryptAccountNumber(dto.BankDetails.AccountNumber);
            model.BankDetails = new NoSupplierBankDetailsModel
            {
                AccountNumber = accountNumberEncrypted,
                RoutingNumber = dto.BankDetails.RoutingNumber,
                AccountType = dto.BankDetails.AccountType
            };
        }

        return await companyApi.UpdateBankAccountNumberModel(bankAccountId, userId, model, ct);
    }

    public async Task<DrawApprovalRecord> PostTransaction(string drawApprovalId, NoteModel note, CancellationToken ct)
    {
        var result = await onboardingApiProxy.PostTransaction(drawApprovalId, mapper.Map<NoteDto>(note), ct);
        return mapper.Map<DrawApprovalRecord>(result);
    }
    public async Task<DrawApprovalRecord> PatchQuoteExpiration(string drawApprovalId, PatchExpirationDateModel model, CancellationToken ct)
    {
        var result = await onboardingApiProxy.PatchQuoteExpirationDate(drawApprovalId, mapper.Map<PatchExpirationDateRequest>(model), ct);
        return mapper.Map<DrawApprovalRecord>(result);
    }

    public Task<LoanPricingPackageDto?> AddLoanPricingPackages(ShortLoanPricingPackageDto package, string userId, CancellationToken ct)
    {
        return loanApi.AddLoanPricingPackages(package, userId, ct);
    }

    public Task<LoanPricingPackageDto?> UpdateLoanPricingPackages(ShortLoanPricingPackageDto package, string userId, string id, CancellationToken ct)
    {
        return loanApi.UpdateLoanPricingPackages(package, id, userId, ct);
    }

    public Task<CardPricingPackageDto?> AddCardPricingPackages(ShortCardPricingPackageDto package, string userId, CancellationToken ct)
    {
        return loanApi.AddCardPricingPackages(package, userId, ct);
    }

    public Task<CardPricingPackageDto?> UpdateCardPricingPackages(ShortCardPricingPackageDto package, string userId, string id, CancellationToken ct)
    {
        return loanApi.UpdateCardPricingPackages(package, id, userId, ct);
    }

    public async Task<CompanyModel?> CreateGuestSupplier(string userId, CreateUpdateGuestSupplierModel createModel, CancellationToken ct)
    {
        ArgumentNullException.ThrowIfNull(createModel);
        var createCompanyModel = new CreateCompanyModel()
        {
            Name = createModel.BusinessName,
            Type = CompanyTypeEnum.Supplier,
            IsGuest = true,
            Status = CompanyStatusEnum.Approved,
            ContactName = createModel.ContactName,
            Email = createModel.Email,
            Phone = createModel.Phone,
            Settings = new CompanySettingsModel
            {
                LoanPricingPackageId = createModel.LoanPricingPackageId,
                DefaultDebtInvestorTradeCredit = createModel.DefaultDebtInvestorTradeCredit ?? DebtInvestorType.Arcadia
            },
            Address = new AddressModel
            {
                Address = createModel.Address?.AddressLine ?? string.Empty,
                City = createModel.Address?.City ?? string.Empty,
                State = createModel.Address?.State ?? string.Empty,
                Zip = createModel.Address?.Zip ?? string.Empty,
                Phone = createModel.Phone ?? string.Empty,
                UnitNumber = string.Empty
            },
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };
        var result = await companyApi.CreateCompany(userId, createCompanyModel, ct);
        if (createModel.BankDetailsRequest == null)
        {
            return result;
        }
        var accountNumberEncrypted = await accountDecryptor.EncryptAccountNumber(createModel.BankDetailsRequest.AccountNumber);
        if (result != null && accountNumberEncrypted.Cipher != null)
        {
            var account = await companyApi.CreateBankAccountAsync(userId, result.Id, new CreateBankAccountModel()
            {
                RoutingNumber = createModel.BankDetailsRequest.RoutingNumber,
                AccountNumberCipher = accountNumberEncrypted.Cipher,
                AccountHolderName = createModel.BusinessName,
                PaymentMethodType = "bank",
                IsPrimary = true,
                IsManualEntry = true,
                AccountType = createModel.BankDetailsRequest.AccountType ?? DecisionSources.BankAccounts.Checking,
                Status = createModel.GiactVerificationResult?.VerificationResult == DecisionSources.BankAccounts.GiactVerifiedStatus ?
                    DecisionSources.BankAccounts.GiactVerifiedStatus : "notverified",

            }, ct);

            if (account == null) return result;

            result.BankAccounts = [account.Id];
            await companyApi.UpdateBankAccountGiactInfoAsync(account.Id, new BankAccountGiactModel()
            {
                LastUpdated = DateTime.UtcNow,
                Accounts =
                [
                    new GiactAccountModel
                    {
                        Id = account.Id,
                        AccountNumberDisplay = accountNumberEncrypted.Display ?? string.Empty,
                        Response = createModel.GiactVerificationResult
                    }
                ]
            }, ct);
        }
        return result;
    }

    /// <summary>
    /// The function is for “Edit Contact Details” and for “Edit Package” button.
    /// </summary>
    /// <param name="id"></param>
    /// <param name="userId"></param>
    /// <param name="updateModel"></param>
    /// <param name="ct"></param>
    /// <returns></returns>
    public async Task<CompanyModel?> UpdateGuestSupplier(string id, string userId, CreateUpdateGuestSupplierModel updateModel, CancellationToken ct)
    {
        ArgumentNullException.ThrowIfNull(updateModel);
        var createCompanyModel = new UpdateCompanyModel
        {
            Name = updateModel.BusinessName,
            Type = CompanyTypeEnum.Supplier,
            IsGuest = true,
            ContactName = updateModel.ContactName,
            Email = updateModel.Email,
            Phone = updateModel.Phone,
            Settings = new CompanySettingsModel
            {
                LoanPricingPackageId = updateModel.LoanPricingPackageId,
                DefaultDebtInvestorTradeCredit = updateModel.DefaultDebtInvestorTradeCredit ?? DebtInvestorType.Arcadia
            },
            Address = new AddressModel
            {
                Address = updateModel.Address?.AddressLine ?? string.Empty,
                City = updateModel.Address?.City ?? string.Empty,
                State = updateModel.Address?.State ?? string.Empty,
                Zip = updateModel.Address?.Zip ?? string.Empty,
                Phone = updateModel.Phone ?? string.Empty,
                UnitNumber = string.Empty
            },
            UpdatedAt = DateTime.UtcNow
        };
        var result = await companyApi.UpdateCompany(id, userId, createCompanyModel, ct);
        return result;
    }


    public async Task<DrawApprovalDetailsRecord> AttachSupplierToDrawApprovals(string drawApprovalId,
        string userId, AttachSupplierToDrawApproval attachSupplierRequest, CancellationToken ct)
    {
        ArgumentNullException.ThrowIfNull(attachSupplierRequest);

        // get company
        var company = await companyApi.GetCompanyById(attachSupplierRequest.SupplierId, ct);
        if (company == null)
        {
            throw new InvalidOperationException($"Company with Id: {attachSupplierRequest.SupplierId} was not found.");
        }

        // BFF is triggered by frontend, supplierId is sent.
        // Will read draw approval's attached invoices and updates them
        var approval = await onboardingApiProxy.GetDrawApproval(drawApprovalId, ct);
        if (approval == null)
        {
            throw new InvalidOperationException($"DrawApproval with Id: {drawApprovalId} was not found.");
        }

        // BFF triggers Invoice service to attach supplier to invoices.
        var invoiceIds = approval.Payables.Select(x => x.Id).ToArray();
        await invoiceApi.PatchInvoicesCompanyIdAsync(new MultiInvoiceCompanyModel()
        { InvoiceIds = invoiceIds, CompanyId = attachSupplierRequest.SupplierId }, ct);

        // BFF triggers OBS to attach merchant to drawApproval. Draw Approval type is still noSupplier!

        var updatedApproval = await onboardingApiProxy.PatchDrawApproval(drawApprovalId,
            new PatchDrawApprovalInternalRequest
            {
                MerchantId = attachSupplierRequest.SupplierId,
                MerchantName = company.Name,
                UpdatedBy = userId,
                UpdatedAt = DateTime.UtcNow,
            }, ct);

        await onboardingApiProxy.AddDrawApprovalNote(drawApprovalId, new CreateDrawApprovalNoteDto()
        { Note = $"Supplier {company.Name} was attached to DrawApproval" }, ct);

        return mapper.Map<DrawApprovalDetailsRecord>(updatedApproval);
    }

    public async Task<CompanyNoteModel?> CreateCompanyNote(string companyId, string userId, CreateCompanyNoteModel dto, CancellationToken ct)
    {
        ArgumentNullException.ThrowIfNull(companyId);
        ArgumentNullException.ThrowIfNull(userId);
        ArgumentNullException.ThrowIfNull(dto);

        var result = await companyApi.CreateCompanyNote(companyId, userId, dto, ct);
        return result;
    }

    public async Task ExecuteRefreshService(string companyId, CancellationToken ct)
    {
        var isAnyRuleOverriden = await IsAnyRuleOverriden(companyId, ct);

        if (isAnyRuleOverriden) await onboardingApiProxy.RerunCalculationsAfterOverride(companyId, ct);
        else
        {
            var model = new ManualRefreshRunModel()
            {
                ExecutionFlow = RefreshServiceManualOverrideExecutionFlow.Rerun
            };
            await onboardingApiProxy.ExecuteRefreshService(companyId, model, ct);
        }
    }

    public Task ClearManualOverrideAndRerunRefreshService(string companyId, ManualRefreshRunModel model, CancellationToken ct)
    {
        ArgumentNullException.ThrowIfNull(model);
        model.ExecutionFlow = RefreshServiceManualOverrideExecutionFlow.ClearOverrides;
        return onboardingApiProxy.ExecuteRefreshService(companyId, model, ct);
    }

    public async Task<RefreshExecutionStatusResult> GetRefreshExecutionStatus(string companyId, CancellationToken ct)
    {
        var refreshSteps = await onboardingApiProxy.GetDecisionStepsByQuery(new DecisionEngineStepQueryDto()
        {
            Statuses = ["started"],
            ExecutionTypes = ["scheduled"],
            CompanyId = companyId
        }, ct);
        if (refreshSteps.Any()) return new RefreshExecutionStatusResult()
        {
            Status = RefreshExecutionStatus.Started
        };

        return new RefreshExecutionStatusResult()
        {
            Status = RefreshExecutionStatus.NotFound
        };
    }

    public Task ReinstateDecisionEngineRule(string id, string? identifier, ReinstateDecisionEngineRuleModel reinstateModel, CancellationToken ct)
    {
        return onboardingApiProxy.ReinstateDecisionEngineRule(id, identifier, reinstateModel, ct);
    }

    public Task IgnoreDecisionEngineRule(string id, string? identifier, IgnoreDecisionEngineRuleModel ignoreModel, CancellationToken ct)
    {
        return onboardingApiProxy.IgnoreDecisionEngineRule(id, identifier, ignoreModel, ct);
    }

    public IEnumerable<string> GetDebtInvestors() =>
        Enum.GetValues(typeof(DebtInvestorType))
            .Cast<DebtInvestorType>()
            .Take(2)  // Take only the 2 enum values (Arcadia and Raistone)
            .Select(e =>
            {
                var fieldInfo = e.GetType().GetField(e.ToString());
                var attribute = fieldInfo?.GetCustomAttribute<EnumMemberAttribute>();
                return attribute?.Value ?? e.ToString();
            })
            .ToList();

    public async Task<BankAccountModel?> CreateManualBankAccount(string companyId, CreateManualBankAccountModel model, string userId, CancellationToken ct)
    {
        if (string.IsNullOrEmpty(model.BankDetails.AccountNumber)) throw new ValidationException("Bank account number is not provided");

        var company = await companyApi.GetCompanyById(companyId, ct);
        var giactResponse = mapper.Map<GiactResponseModel>(model.GiactVerificationResult);
        var encryptedAccountNumber = await accountDecryptor.EncryptAccountNumber(model.BankDetails.AccountNumber);
        var giactObject = giactResponse.ToBankAccountGiactModel(encryptedAccountNumber.Display);

        var createBankAccount = new CreateBankAccountModel()
        {
            IsPrimary = false,
            IsManualEntry = true,
            AccountHolderName = company?.LegalName,
            AccountName = null,
            IsDeactivated = false,
            Plaid = null,
            Status = "verified",
            PaymentMethodType = "bank",
            RoutingNumber = model.BankDetails.RoutingNumber,
            AccountNumberCipher = encryptedAccountNumber.Cipher!,
            AccountType = model.BankDetails.AccountType,
            Giact = giactObject,
            IncludeInCashFlow = false,
        };

        var bankAccount = (await companyApi.CreateBankAccountAsync(userId, companyId, createBankAccount, ct))!;

        return bankAccount;
    }

    private async Task<bool> IsAnyRuleOverriden(string companyId, CancellationToken ct)
    {
        var latestExecutions = await onboardingApiProxy.GetLatestDeStepsByCompanyId(companyId, null, ct);
        var results = latestExecutions.SelectMany(x => x.Results ?? new List<DecisionEngineStepResultDto>());
        var resultsOverridenManually = results.Where(x => !string.IsNullOrEmpty(x.ManualResult));
        return resultsOverridenManually.Any();
    }

    private static string GetCustomerName(IEnumerable<UserModel> users)
    {
        var user = users.Where(user => !string.IsNullOrEmpty(user.FirstName) && !string.IsNullOrEmpty(user.LastName) || !string.IsNullOrEmpty(user.ContactName))
            .MinBy(user => user.CreatedAt);

        return user != null
            ? !string.IsNullOrEmpty(user.FirstName) && !string.IsNullOrEmpty(user.LastName)
                ? $"{user.FirstName} {user.LastName}"
                : user.ContactName!
            : string.Empty;
    }
}
