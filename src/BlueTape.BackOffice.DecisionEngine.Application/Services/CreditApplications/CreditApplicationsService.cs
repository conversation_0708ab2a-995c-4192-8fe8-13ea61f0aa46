using AutoMapper;
using BlueTape.BackOffice.DecisionEngine.Application.Constants;
using BlueTape.BackOffice.DecisionEngine.Application.Extensions;
using BlueTape.BackOffice.DecisionEngine.Application.Services.Common.Generators;
using BlueTape.BackOffice.DecisionEngine.Application.Services.CreditApplications.Generators;
using BlueTape.BackOffice.DecisionEngine.Application.Services.CreditApplications.Models;
using BlueTape.BackOffice.DecisionEngine.Application.Services.CreditApplications.Models.DecisionEngineExecutions;
using BlueTape.BackOffice.DecisionEngine.Application.Services.CreditApplications.Models.DocumentVerification;
using BlueTape.BackOffice.DecisionEngine.DataAccess.External.CompanyApi;
using BlueTape.BackOffice.DecisionEngine.DataAccess.External.Constants;
using BlueTape.BackOffice.DecisionEngine.DataAccess.External.LoanApi.Proxy;
using BlueTape.BackOffice.DecisionEngine.DataAccess.External.OnboardingApi;
using BlueTape.BackOffice.DecisionEngine.Domain.Enums;
using BlueTape.BackOffice.DecisionEngine.Domain.Enums.UI;
using BlueTape.BackOffice.DecisionEngine.Domain.Extensions;
using BlueTape.BackOffice.DecisionEngine.Domain.Models;
using BlueTape.CompanyService.CashFlow.Queries;
using BlueTape.CompanyService.Companies;
using BlueTape.CompanyService.Enums;
using BlueTape.Firebase.Abstractions;
using BlueTape.LS.Domain.Enums;
using BlueTape.LS.Domain.Models;
using BlueTape.LS.DTOs.CardPricingPackages;
using BlueTape.LS.DTOs.Credit;
using BlueTape.LS.DTOs.LoanPricingPackages;
using BlueTape.OBS.DTOs.CreditApplication;
using BlueTape.OBS.DTOs.CreditApplication.Queries;
using BlueTape.OBS.DTOs.CreditApplicationAuthorizationDetails;
using BlueTape.OBS.DTOs.DecisionEngineSteps;
using BlueTape.OBS.Enums;
using BlueTape.Utilities.Extensions;
using BlueTape.Utilities.Providers;
using CompanyIdsDto = BlueTape.OBS.DTOs.CreditApplication.Queries.CompanyIdsDto;
using CompanyModel = BlueTape.CompanyService.Companies.CompanyModel;
using CreditApplicationStatus = BlueTape.BackOffice.DecisionEngine.Domain.Enums.CreditApplicationStatus;

namespace BlueTape.BackOffice.DecisionEngine.Application.Services.CreditApplications;

public class CreditApplicationsService(
    IOnboardingApiProxy _onboardingApi,
    ICompanyApiProxy _companyApiProxy,
    IDateProvider _dateProvider,
    IMapper _mapper,
    IFirebaseClient _firebaseClient,
    ILoanApiProxy _loanApiProxy)
    : ICreditApplicationsService
{
    public Task ReviewCreditApplication(string creditApplicationId, ReviewCreditApplicationDto reviewCreditApplicationDto, CancellationToken ct)
    {
        ArgumentException.ThrowIfNullOrEmpty(creditApplicationId);
        ArgumentNullException.ThrowIfNull(reviewCreditApplicationDto);

        if (string.IsNullOrEmpty(reviewCreditApplicationDto.NewStatus))
            throw new InvalidOperationException("The new status must be provided");

        var newStatus = reviewCreditApplicationDto.NewStatus.ToEnum<CreditApplicationStatus>();
        if (!newStatus.HasValue) throw new InvalidOperationException($"Unknown status {reviewCreditApplicationDto.NewStatus}");

        return _onboardingApi.ReviewCreditApplication(creditApplicationId, reviewCreditApplicationDto, ct);
    }

    public async Task<CreditApplicationDetails> GetCreditApplicationDetails(string applicationId, CancellationToken ct)
    {
        ArgumentException.ThrowIfNullOrEmpty(applicationId);

        var application = await _onboardingApi.GetCreditApplication(applicationId, ct).EnsureExists($"The credit application {applicationId} was not found");
        var authDetailsRequest = _onboardingApi.GetApplicationAuthorizationDetails(applicationId, ct);
        var decisionEngineStepsRequest = GetDecisionEngineSteps(application?.ExecutionId, ct);
        var creditRequest = GetCreditByCreditApplication(application!, ct);

        await Task.WhenAll(decisionEngineStepsRequest, authDetailsRequest, creditRequest);

        var decisionSteps = (await decisionEngineStepsRequest).ToList();
        var authDetails = await authDetailsRequest;
        var credit = await creditRequest;

        if (string.IsNullOrEmpty(application!.CompanyId))
            throw new InvalidOperationException("Unable to request bank accounts: the company id is not provided");

        var status = application.Status.ToEnum<CreditApplicationStatus>() ?? CreditApplicationStatus.ExecutionFailed;
        var automatedDecision = application.AutomatedDecisionResult.ToEnum<AutomatedDecisionType>();
        var automatedDecisionStatus = DecisionEngineExtensions.CalculateCreditApplicationAutomatedDecisionStatus(status, automatedDecision);

        var cashFlowQuery = GetCashFlowQuery();
        var bankAccountsRequest = _companyApiProxy.GetBankAccountsByCompanyId(application.CompanyId, ct);
        var cashFlowRequest = _companyApiProxy.GetCashFlow(application.CompanyId, cashFlowQuery, ct);
        var bankStatementsRequest = _companyApiProxy.GetBankStatements(application.CompanyId, ct);
        var companyDocumentsInfoRequest = _companyApiProxy.GetDocuments(application.CompanyId, ct);
        var accountAuthRequest = _onboardingApi.GetAccountAuthorizationByCompanyId(application.CompanyId, ct);
        var companyRequest = _companyApiProxy.GetCompanyById(application.CompanyId, ct);

        await Task.WhenAll(bankAccountsRequest, cashFlowRequest, bankStatementsRequest, companyDocumentsInfoRequest, accountAuthRequest, companyRequest);

        var bankStatementResponseItems = await bankStatementsRequest;
        var bankAccounts = await bankAccountsRequest;
        var cashFlow = _mapper.Map<CashFlowDetails>(await cashFlowRequest) ?? new CashFlowDetails();
        var bankStatements = _mapper.Map<List<BankStatementDocument>>(bankStatementResponseItems);
        var companyDocumentsInfo = await companyDocumentsInfoRequest;
        var lastStatusChangedBy = await DecisionEngineExtensions.ParseLastStatusChangedBy(application.LastStatusChangedBy, _firebaseClient, ct);
        var accountAuth = await accountAuthRequest;
        var company = await companyRequest;
        var executionId = application.ExecutionId;
        var businessCategoryType = ParseBusinessCategoryType(application);

        var (currentLoanPackageName, currentCardPackageName) = application.Type is CreditApplicationType.GetPaid
            ? await GetCurrentPricingPackages(company, ct)
            : (string.Empty, string.Empty);

        var currentCreditLimit = application.CreditLimit;
        decimal creditAvailableBalance = 0;
        decimal creditOutstandingBalance = 0;
        if (status == CreditApplicationStatus.Approved && application.Type is CreditApplicationType.InHouseCredit or CreditApplicationType.ARAdvance)
        {
            var creditDetailDto = await GetCreditByCreditApplication(application, ct);
            currentCreditLimit = creditDetailDto?.CreditLimit;
            creditAvailableBalance = creditDetailDto?.CreditDetails.AvailableCredit ?? 0;
            creditOutstandingBalance = creditDetailDto?.CreditDetails.OutstandingCredit ?? 0;
        }

        return new CreditApplicationDetails
        {
            Id = application.Id,
            CreditId = credit?.Id,
            CompanyId = application.CompanyId,
            MerchantId = application.MerchantId,
            BankAccount = application.BankAccountType,
            IsArAdvanceRequested = application.IsArAdvanceRequested,
            ArAdvanceApplicationId = application.ArAdvanceApplicationId,
            BusinessName = application.BusinessName,
            BusinessDba = application.BusinessDba,
            AutomatedDecision = automatedDecision,
            AutomatedDecisionStatus = automatedDecisionStatus,
            Status = status,
            Category = businessCategoryType,
            UIStatus = status.ToUIStatus(),
            MerchantName = await GetMerchantName(application, ct),
            CreatedAt = application.CreatedAt.HasValue ? DateOnly.FromDateTime(application.CreatedAt.Value) : null,
            ApprovedAt = application.ApprovedAt.HasValue ? DateOnly.FromDateTime(application.ApprovedAt.Value) : null,
            ApprovedBy = !string.IsNullOrEmpty(application.ApprovedBy) ? await DecisionEngineExtensions.ParseLastStatusChangedBy(application.ApprovedBy, _firebaseClient, ct) : null,
            IsSecured = company?.Settings?.DepositDetails?.IsSecured,
            IsDepositPaid = company?.Settings?.DepositDetails?.IsDepositPaid,
            DepositAmount = company?.Settings?.DepositDetails?.DepositAmount,

            ApplicationDate = application.ApplicationDate.HasValue ? DateOnly.FromDateTime(application.ApplicationDate.Value) : null,
            Type = application.Type,
            PurchaseTypeOption = application.PurchaseTypeOption,
            LastStatusChangedAt = application.LastStatusChangedAt,
            LastStatusChangedBy = lastStatusChangedBy,
            RequestedAmount = GetCreditApplicationRequestedAmount(application.RequestedAmount),
            ApprovedAmount = application.ApprovedCreditLimit,
            CurrentCreditLimit = currentCreditLimit,
            CreditAvailableBalance = creditAvailableBalance,
            CreditOutstandingBalance = creditOutstandingBalance,
            CreditLimit = application.CreditLimit,
            StatusCode = application.StatusCode,
            StatusNote = application.StatusNote,
            IsInHouseCreditEnabled = application.IsInHouseCreditEnabled,
            GetPaidApplicationId = application.GetPaidApplicationId,
            GetPaidApprovedBy = !string.IsNullOrEmpty(application.GetPaidApprovedBy) ? await DecisionEngineExtensions.ParseLastStatusChangedBy(application.GetPaidApprovedBy, _firebaseClient, ct) : null,
            GetPaidCreatedAt = application.GetPaidCreatedAt,
            GetPaidApplicationDate = application.GetPaidApplicationDate,
            GetPaidApprovalDate = application.GetPaidApprovalDate,
            CardPricingPackageName = application.MerchantSettings?.CardPricingPackageName,
            LoanPricingPackageName = application.MerchantSettings?.LoanPricingPackageName,
            CurrentCardPricingPackageName = currentCardPackageName,
            CurrentLoanPricingPackageName = currentLoanPackageName,
            DocumentVerification = DocumentVerificationGenerator.Create(companyDocumentsInfo, authDetails, bankStatements),
            PreliminaryChecks = PreliminaryChecksGenerator.Create(decisionSteps),
            FactoringPreliminaryChecks = FactoringPreliminaryChecksGenerator.Create(decisionSteps),
            Kyb = KybGenerator.Create(authDetails, decisionSteps),
            Kyc = KycGenerator.Create(authDetails, decisionSteps),
            BankDetails = BankAccountsGenerator.Create(authDetails, accountAuth, bankAccounts, decisionSteps, cashFlow.CashFlowItems, credit),
            CreditRating = CreditRatingGenerator.Create(authDetails, decisionSteps, _dateProvider),
            ResultsDescription = ResultsDescriptionGenerator.GenerateResultsDescriptions(executionId, decisionSteps),
        };
    }

    public async Task<IReadOnlyList<CreditApplicationRecord>> GetCreditApplicationsForSpecificOwner(string ssnHash, CreditApplicationsOwnerListFilter filter, CancellationToken ct)
    {
        ArgumentNullException.ThrowIfNull(filter);

        var query = _mapper.Map<CreditApplicationQueryDto>(filter);
        query.Status = GetStatusFilters(filter.Status);
        query.SsnHashes = [ssnHash];

        var response = await _onboardingApi.GetCreditApplicationsList(query, ct);

        var items = await Task.WhenAll(response.Select(async app => await BuildCreditApplicationRecord(app, false, ct)));

        return items.ToList();
    }

    public async Task<PaginatedList<CreditApplicationRecord>> GetCreditApplicationsList(CreditApplicationsListFilter filter, CancellationToken ct)
    {
        ArgumentNullException.ThrowIfNull(filter);

        var query = _mapper.Map<CreditApplicationQueryPaginatedDto>(filter);

        query.Status = GetStatusFilters(filter.Status);
        query.Category = filter.Category?.Select(x => x.ToString()).ToArray();
        query.AutomatedDecision = filter.AutomatedDecision?.Select(x => x.ToString()).ToArray();
        query.BankAccountType = filter.BankAccountType?.Select(x => x.ToString()).ToArray();

        (query.SortBy, query.SortOrder) = MapSortOptions(filter);

        var response = await _onboardingApi.GetCreditApplicationsPaginatedList(query, ct);
        var items = await Task.WhenAll(response.Result.Select(async app => await BuildCreditApplicationRecord(app, query.CreditDetails, ct)));

        return new PaginatedList<CreditApplicationRecord>
        {
            Result = items,
            TotalCount = response.TotalCount,
            PageNumber = response.PageNumber,
            PagesCount = response.PagesCount
        };
    }

    private async Task<CreditApplicationRecord> BuildCreditApplicationRecord(CreditApplicationDto app, bool? fillCreditDetails, CancellationToken ct)
    {
        var status = app.Status.ToEnum<CreditApplicationStatus>() ?? CreditApplicationStatus.ExecutionFailed;
        var uiStatus = status.ToUIStatus();
        var automatedDecision = app.AutomatedDecisionResult.ToEnum<AutomatedDecisionType>();
        CreditApplicationDetailsModel? creditDetails = null;
        var currentCreditLimit = app.CreditLimit;
        if (fillCreditDetails == true && app.Type is CreditApplicationType.InHouseCredit or CreditApplicationType.ARAdvance)
        {
            var creditDetailDto = await GetCreditByCreditApplication(app, ct);
            if (creditDetailDto?.CreditDetails != null)
            {
                if (uiStatus == UICreditApplicationStatus.Approved)
                {
                    currentCreditLimit = creditDetailDto.CreditLimit;
                }

                creditDetails = _mapper.Map<CreditApplicationDetailsModel>(creditDetailDto.CreditDetails);
                creditDetails.Id = creditDetailDto.Id;
                creditDetails.TotalCredit = creditDetailDto.CreditLimit;
                creditDetails.CurrentCredit = creditDetailDto.CreditDetails.AvailableCredit + creditDetailDto.CreditDetails.ProcessingAmount;
            }
        }

        return new CreditApplicationRecord
        {
            Id = app.Id,
            CompanyId = app.CompanyId,
            BusinessName = app.BusinessName,
            BankAccount = app.BankAccountType,
            IsArAdvanceRequested = app.IsArAdvanceRequested,
            GetPaidApprovalDate = app.GetPaidApprovalDate.HasValue ? app.GetPaidApprovalDate.ToShortDateOnly() : null,
            GetPaidApprovedBy = !string.IsNullOrEmpty(app.GetPaidApprovedBy) ? await DecisionEngineExtensions.ParseLastStatusChangedBy(app.GetPaidApprovedBy, _firebaseClient, ct) : null,
            Dba = string.IsNullOrEmpty(app.BusinessDba) ? null : app.BusinessDba,
            MerchantId = app.MerchantId,
            MerchantName = await GetMerchantName(app, ct),
            Type = app.Type,
            Category = ParseBusinessCategoryType(app),
            ApplicantName = app.ApplicantName,
            LoanPricingPackageName = app.MerchantSettings?.LoanPricingPackageName,
            CardPricingPackageName = app.MerchantSettings?.CardPricingPackageName,
            LoanPricingPackageId = app.MerchantSettings?.LoanPricingPackageId,
            CardPricingPackageId = app.MerchantSettings?.CardPricingPackageId,
            ArAdvanceApplicationId = app.ArAdvanceApplicationId,
            IsInHouseCreditEnabled = app.IsInHouseCreditEnabled,
            SubmissionDate = app.ApplicationDate.ToShortDateOnly(),
            DecisionDate = IsInReview(status) ? app.LastStatusChangedAt.ToShortDateOnly() : null,
            DecisionMadeBy = IsInReview(status) ? await DecisionEngineExtensions.ParseLastStatusChangedBy(app.LastStatusChangedBy, _firebaseClient, ct) : null,
            RequestedAmount = GetCreditApplicationRequestedAmount(app.RequestedAmount),
            ApprovedAmount = app.ApprovedCreditLimit,
            CreditLimit = app.CreditLimit,
            Currency = Currencies.Usd,
            Status = status,
            CurrentCreditLimit = currentCreditLimit,
            UIStatus = uiStatus,
            AutomatedDecision = automatedDecision,
            BankAccountType = app.BankAccountType,
            CreatedAt = app.CreatedAt.HasValue ? DateOnly.FromDateTime(app.CreatedAt.Value) : null,
            CreditDetails = creditDetails ?? _mapper.Map<CreditApplicationDetailsModel>(app.CreditDetails),
            AutomatedDecisionStatus = DecisionEngineExtensions.CalculateCreditApplicationAutomatedDecisionStatus(status, automatedDecision)
        };
    }

    public async Task<DeLastExecutionsDetailed> GetDeLastExecutionDetailed(string companyId, UICreditApplicationType type, CancellationToken ct)
    {
        ArgumentException.ThrowIfNullOrEmpty(companyId);
        var creditApplicationType = type.UITypeToCreditApplicationType();
        var application = (await _onboardingApi.GetCreditApplicationsList(new CreditApplicationQueryDto
        { CompanyId = companyId, Type = [creditApplicationType.ToString()] }, ct)).MaxBy(x => x.CreatedAt);

        var decisionEngineStepsRequest = _onboardingApi.GetLatestDeStepsByCompanyId(companyId, creditApplicationType, ct);
        var companyDocumentsInfoRequest = _companyApiProxy.GetDocuments(companyId, ct);
        var bankStatementsRequest = _companyApiProxy.GetBankStatements(companyId, ct);
        var bankAccountsRequest = _companyApiProxy.GetBankAccountsByCompanyId(companyId, ct);
        var accountAuthRequest = _onboardingApi.GetAccountAuthorizationByCompanyId(companyId, ct);
        var cashFlowRequest = _companyApiProxy.GetCashFlow(companyId, GetCashFlowQuery(), ct);
        var creditRequest = GetCreditByCreditApplication(application, ct);
        var refreshStepsRequest = _onboardingApi.GetDecisionStepsByQuery(new DecisionEngineStepQueryDto()
        {
            Statuses = ["started"],
            ExecutionTypes = ["scheduled"],
            CompanyId = companyId
        }, ct);


        await Task.WhenAll(decisionEngineStepsRequest, companyDocumentsInfoRequest, bankStatementsRequest, accountAuthRequest, cashFlowRequest, creditRequest, refreshStepsRequest);

        var decisionSteps = (await decisionEngineStepsRequest).ToList();
        var refreshSteps = (await refreshStepsRequest).ToList();

        var executionStatus = refreshSteps.Count != 0 ? RefreshExecutionStatus.Started : RefreshExecutionStatus.NotFound;

        var companyDocumentsInfo = await companyDocumentsInfoRequest;
        var backStatements = _mapper.Map<List<BankStatementDocument>>(await bankStatementsRequest);
        var bankAccounts = (await bankAccountsRequest).ToList();
        var accountAuth = await accountAuthRequest;
        var cashFlow = _mapper.Map<CashFlowDetails>(await cashFlowRequest);
        var credit = await creditRequest;
        var creditAppAuthDetails = new CreditApplicationAuthorizationDetailsDto()
        {
            AccountAuthorizationDetailsSnapshot = accountAuth
        };

        return new DeLastExecutionsDetailed
        {
            ExecutionStatus = executionStatus,
            PreliminaryChecks = PreliminaryChecksGenerator.CreateDeExecution(decisionSteps),
            Kyb = KybGenerator.CreateDeExecution(creditAppAuthDetails, decisionSteps),
            Kyc = KycGenerator.CreateDeExecution(creditAppAuthDetails, decisionSteps),
            CreditRating = CreditRatingGenerator.CreateDeExecution(creditAppAuthDetails, decisionSteps, _dateProvider),
            BankDetails = BankAccountsGenerator.CreateDeExecution(creditAppAuthDetails, accountAuth, bankAccounts, decisionSteps, cashFlow.CashFlowItems, credit),
            DocumentVerification = DocumentVerificationGenerator.Create(companyDocumentsInfo, creditAppAuthDetails, backStatements),
            CashFlowDetails = new DeCashFlowDetails { LastRefreshedAt = cashFlow.UpdatedAt }
        };
    }

    private static BusinessCategoryType? ParseBusinessCategoryType(CreditApplicationDto app)
    {
        // we need to parse values as Sub-Contractor
        var businessCategory = app.BusinessCategory?.Replace("-", string.Empty);
        var category = businessCategory.ToEnum<BusinessCategoryType>(true);

        return category;
    }

    private static decimal? GetCreditApplicationRequestedAmount(decimal? requestedAmount)
    {
        if (requestedAmount is null or 0) return null;

        return requestedAmount.Value;
    }

    public Task<string> GetLexisNexisRawData(string creditApplicationId, LexisNexisSourceType type, string? reference, CancellationToken ct)
    {
        return _onboardingApi.GetLexisNexisRawData(creditApplicationId, type, reference, ct);
    }

    public Task<string> GetExperianRawData(string creditApplicationId, ExperianSourceType type, CancellationToken ct)
    {
        return _onboardingApi.GetExperianRawData(creditApplicationId, type, ct);
    }

    public Task<string> GetGiactRawData(string creditApplicationId, CancellationToken ct)
    {
        return _onboardingApi.GetGiactRawData(creditApplicationId, ct);
    }

    private static (string Field, string? Order) MapSortOptions(CreditApplicationsListFilter filter)
    {
        var defaultSort = (CreditApplicationFields.ApplicationDate, SortOrders.Descending);
        if (!filter.SortBy.HasValue) return defaultSort;
        var sortBy = filter.SortBy.Value.MapCreditApplicationFilter();

        return (sortBy, filter.SortOrder);
    }

    private AssetReportQuery GetCashFlowQuery()
    {
        return new AssetReportQuery()
        {
            From = _dateProvider.CurrentDateTime.AddYears(CashFlowFilters.DateFromYearsBeforeCurrentDate),
            Grouping = CashFlowGrouping.Monthly
        };
    }

    private static string[] GetStatusFilters(UICreditApplicationStatus[]? status)
    {
        if (status == null || status.Length == 0) return Enumerable.Empty<string>().ToArray();

        var result = new List<string>();

        foreach (var statusFilter in status)
        {
            result.AddRange(CreditApplicationFilters.StatusFilterMapping[statusFilter]);
        }

        return result.ToArray();
    }

    private static bool IsInReview(CreditApplicationStatus status)
    {
        return status is not (CreditApplicationStatus.New or CreditApplicationStatus.Processing or CreditApplicationStatus.Processed);
    }

    private async Task<string?> GetMerchantName(CreditApplicationDto creditApp, CancellationToken ct)
    {
        return creditApp.Type switch
        {
            CreditApplicationType.LineOfCredit => creditApp.BusinessName,
            CreditApplicationType.InHouseCredit => !string.IsNullOrEmpty(creditApp.MerchantId)
                ? (await _companyApiProxy.GetCompanyById(creditApp.MerchantId!, ct))?.Name
                : string.Empty,
            _ => creditApp.BusinessName
        };
    }

    private async Task<CreditDto?> GetCreditByCreditApplication(CreditApplicationDto dto, CancellationToken ct)
    {
        var result = (await _loanApiProxy.GetCreditsByFilters(new CreditFilterDto
        {
            CompanyId = dto.CompanyId,
            MerchantId = dto.MerchantId,
            Detailed = true,
            Product = dto.Type.ToString().ParseToEnum<ProductType>()
        }, ct));

        return result.FirstOrDefault();
    }

    private async Task<IEnumerable<DecisionEngineStepsDto>> GetDecisionEngineSteps(string? executionId, CancellationToken ct)
    {
        if (!string.IsNullOrEmpty(executionId))
            return await _onboardingApi.GetDecisionEngineStepsByExecutionId(executionId, ct);

        return ArraySegment<DecisionEngineStepsDto>.Empty;
    }

    private async Task<(string loanPackageName, string cardPackageName)> GetCurrentPricingPackages(CompanyModel? company, CancellationToken ct)
    {
        if (company is null) return (String.Empty, String.Empty);

        var loanPackageTask = !string.IsNullOrEmpty(company.Settings.LoanPricingPackageId)
            ? _loanApiProxy.GetLoanPricingPackagesByFilter(
                new LoanPricingPackagesFilter
                {
                    Name = company.Settings.LoanPricingPackageId
                }, ct)
            : Task.FromResult<List<LoanPricingPackageDto>?>(null);

        var cardPackageTask = !string.IsNullOrEmpty(company.Settings?.CardPricingPackageId)
            ? _loanApiProxy.GetCardPricingPackagesByFilter(
                new CardPricingPackagesFilter
                {
                    Name = company.Settings.CardPricingPackageId
                }, ct)
            : Task.FromResult<List<CardPricingPackageDto>?>(null);

        await Task.WhenAll(loanPackageTask, cardPackageTask);

        var loanPackage = await loanPackageTask;
        var cardPackage = await cardPackageTask;

        return (
            loanPackageName: loanPackage?.FirstOrDefault()?.Title ?? string.Empty,
            cardPackageName: cardPackage?.FirstOrDefault()?.Title ?? string.Empty
        );
    }

    public async Task<IReadOnlyList<CompanyModel>> GetCreditApplicationCompanies(CreditApplicationCompaniesFilter filter, CancellationToken ct)
    {
        ArgumentNullException.ThrowIfNull(filter);

        // First path: When we have name filter
        if (!string.IsNullOrWhiteSpace(filter.Name))
        {
            // Get companies by name
            var companies = (await _companyApiProxy.GetCompaniesList(new CompanyQueryPaginated
            {
                Name = filter.Name,
                PageSize = int.MaxValue,
                PageNumber = 1
            }, ct)).Result;

            // If no companies found by name, return an empty list
            if (companies.Count == 0)
            {
                return [];
            }

            // Prepare query for credit applications
            var query = new GetCreditApplicationsByCompanyIdsQueryDto();
            if (filter.Type.HasValue)
            {
                var creditApplicationType = filter.Type.Value.UITypeToCreditApplicationType();
                query.Type = [creditApplicationType.ToString()];
            }

            if (filter.Status.HasValue)
            {
                query.Status = GetStatusFilters([filter.Status.Value]);
            }

            // Get credit applications for found companies and intersect
            var creditApplications = await _onboardingApi.GetCreditApplicationsByCompanyIds(
                new CompanyIdsDto { Ids = companies.Select(c => c.Id).Distinct().ToArray() },
                query,
                ct);

            // Get unique company IDs from filtered credit applications
            var filteredCompanyIds = creditApplications
                .Select(x => x.CompanyId)
                .Where(x => !string.IsNullOrEmpty(x))
                .Distinct()
                .ToArray();

            return filteredCompanyIds.Length > 0
                ? companies.Where(c => filteredCompanyIds.Contains(c.Id)).DistinctBy(c => c.Id).ToList()
                : [];
        }

        // Second path: When we don't have a name filter,
        // Get credit applications first
        var applicationQueryDto = new CreditApplicationQueryDto();
        if (filter.Type.HasValue)
        {
            var creditApplicationType = filter.Type.Value.UITypeToCreditApplicationType();
            applicationQueryDto.Type = [creditApplicationType.ToString()];
        }

        if (filter.Status.HasValue)
        {
            applicationQueryDto.Status = GetStatusFilters([filter.Status.Value]);
        }

        var creditApplicationsList = await _onboardingApi.GetCreditApplicationsList(applicationQueryDto, ct);

        var companyIds = creditApplicationsList
            .Select(x => x.CompanyId)
            .Where(x => !string.IsNullOrEmpty(x))
            .Select(x => x!)
            .Distinct()
            .ToArray();

        if (companyIds.Length == 0)
        {
            return [];
        }

        // Get companies by IDs from credit applications
        var companiesList = await _companyApiProxy.GetCompaniesByIds(companyIds, ct);
        return companiesList.DistinctBy(c => c.Id).ToList();
    }
}
