using AutoMapper;
using BlueTape.BackOffice.DecisionEngine.Application.Constants;
using BlueTape.BackOffice.DecisionEngine.Application.Extensions;
using BlueTape.BackOffice.DecisionEngine.Application.Messages;
using BlueTape.BackOffice.DecisionEngine.Application.Messages.Models;
using BlueTape.BackOffice.DecisionEngine.Application.Senders.Abstractions;
using BlueTape.BackOffice.DecisionEngine.Application.Services.Admin.Models.Payments;
using BlueTape.BackOffice.DecisionEngine.Application.Services.BankAccounts;
using BlueTape.BackOffice.DecisionEngine.Application.Services.BankAccounts.Models;
using BlueTape.BackOffice.DecisionEngine.Application.Services.Node;
using BlueTape.BackOffice.DecisionEngine.Application.Services.Payments.Models;
using BlueTape.BackOffice.DecisionEngine.Application.Services.Reports;
using BlueTape.BackOffice.DecisionEngine.DataAccess.External.CompanyApi;
using BlueTape.BackOffice.DecisionEngine.DataAccess.External.Constants;
using BlueTape.BackOffice.DecisionEngine.DataAccess.External.DI.Constants;
using BlueTape.BackOffice.DecisionEngine.DataAccess.External.LoanApi.Proxy;
using BlueTape.BackOffice.DecisionEngine.DataAccess.External.OnboardingApi;
using BlueTape.BackOffice.DecisionEngine.DataAccess.External.PaymentApi.Proxy;
using BlueTape.BackOffice.DecisionEngine.Domain.Enums;
using BlueTape.BackOffice.DecisionEngine.Domain.Exceptions;
using BlueTape.BackOffice.DecisionEngine.Domain.Models;
using BlueTape.CompanyService.BankAccounts;
using BlueTape.CompanyService.Companies;
using BlueTape.Firebase.Abstractions;
using BlueTape.Integrations.Aion.Infrastructure.Enums;
using BlueTape.Integrations.Aion.Transactions;
using BlueTape.LS.Domain.Enums;
using BlueTape.LS.DTOs.ChangeLog;
using BlueTape.LS.DTOs.Enums;
using BlueTape.LS.DTOs.Loan;
using BlueTape.OBS.DTOs;
using BlueTape.OBS.DTOs.Linqpal;
using BlueTape.PaymentService.Enums;
using BlueTape.PaymentService.Models;
using BlueTape.ServiceBusMessaging.Attributes;
using BlueTape.Utilities.Providers;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using System.Diagnostics.CodeAnalysis;
using System.Text.Json;

namespace BlueTape.BackOffice.DecisionEngine.Application.Services.Payments;

public class BackofficePaymentService(
    IPaymentApiProxy paymentProxy,
    IOnboardingApiProxy onboardingApiProxy,
    ICompanyApiProxy companyApiProxy,
    IBankAccountsService bankAccountsService,
    ILoanApiProxy loanApiProxy,
    IDrawRepaymentMessageSender drawRepaymentMessageSender,
    IInvoicePaymentMessageSender invoicePaymentMessageSender,
    IDrawRepaymentManualMessageSender drawRepaymentManualMessageSender,
    ISubscriptionPaymentMessageSender subscriptionPaymentMessageSender,
    IMapper mapper,
    IReportFileGenerator reportFileGenerator,
    IDateProvider dateProvider,
    IFirebaseClient firebaseClient,
    INodeService nodeService,
    IConfiguration configuration)
    : IBackofficePaymentService
{
    private const decimal Tolerance = 0.01m;


    public async Task<List<AionTransaction>?> GetAccountTransactions(AccountCodeType accountCode, CancellationToken ct)
    {
        var request = new TransactionsQuery()
        {
            AccountCodeType = accountCode,
            PageNumber = 1,
            PageSize = 200,
        };

        var transactions = await paymentProxy.GetAccountTransactions(request, ct);

        var result = mapper.Map<List<AionTransaction>>(transactions?.Result);

        return result;
    }

    public List<AionAccountShort> GetManualPaymentAccounts(string userId, CancellationToken ct)
    {
        var accounts = new List<AionAccountShort>
        {
            new()
            {
                Name = PaymentServiceConstants.LockboxAccountName,
                Code = AccountCodeType.LOCKBOXCOLLECTION.ToString()
            },
            new()
            {
                Name = PaymentServiceConstants.DacaAccountName,
                Code = AccountCodeType.DACACOLLECTION.ToString()
            },
        };

        return accounts;
    }

    public async Task<AionAccount> GetAccountByTypeCode(AccountCodeType accountCodeType, CancellationToken ct)
    {
        var accounts = await paymentProxy.GetAllAccounts("aion", ct);

        return accounts?.FirstOrDefault(x => x.AccountCodeType == accountCodeType);
    }

    public async Task<GetQueryWithPaginationResultDto<PaymentResponse>> GetDisbursements(
        PaymentRequestFilterQuery query,
        CancellationToken cancellationToken)
    {
        ArgumentNullException.ThrowIfNull(query);

        query.Status ??=
        [
            nameof(PaymentRequestStatus.Requested),
            nameof(PaymentRequestStatus.Processing),
            nameof(PaymentRequestStatus.Processed),
            nameof(PaymentRequestStatus.Settled)
        ];

        // If we gets CREATE.FACTORING.FINAL_PAYMENT, we need to add FinalPaymentV2 as well
        if (query.FlowTemplateCodes is not null &&
            query.FlowTemplateCodes.Contains(FlowTemplateCodes.FinalPayment, StringComparer.OrdinalIgnoreCase))
        {
            query.FlowTemplateCodes.Add(FlowTemplateCodes.FinalPaymentV2);
        }

        var result = await paymentProxy.GetPaymentRequests(query, cancellationToken);
        if (result?.Result is null)
            return new GetQueryWithPaginationResultDto<PaymentResponse>();

        var paymentRequests = result.Result.ToList();

        var payerIds = paymentRequests
            .Where(r => !string.IsNullOrEmpty(r.PayerId))
            .Select(r => r.PayerId);

        var sellerIds = paymentRequests
            .Where(r => !string.IsNullOrEmpty(r.SellerId))
            .Select(r => r.SellerId);

        var payeeIds = paymentRequests
            .Where(r => !string.IsNullOrEmpty(r.PayeeId))
            .Select(r => r.PayeeId);

        var allCompanyIds = payerIds
            .Union(sellerIds)
            .Union(payeeIds)
            .Where(x => !string.IsNullOrEmpty(x))
            .Select(x => x!)
            .Distinct()
            .ToList();

        var companiesList = await companyApiProxy.GetCompaniesByIds(allCompanyIds.ToArray(), cancellationToken);

        var companiesDict = companiesList
            .ToDictionary(c => c.Id, c => c, StringComparer.OrdinalIgnoreCase);

        var allPayableIds = paymentRequests
            .Where(r => r.PaymentRequestPayables != null)
            .SelectMany(r => r.PaymentRequestPayables.Select(x => x.PayableId))
            .Distinct()
            .ToArray();

        var drawApprovals = await onboardingApiProxy
            .GetDrawApprovalsByInvoicesIds(allPayableIds, cancellationToken);

        var invoiceToDrawApprovalDict = drawApprovals?
            .Where(da => da != null)
            .SelectMany(da => da!.Payables.Select(p => new { InvoiceId = p.Id, DrawApproval = da }))
            .GroupBy(x => x.InvoiceId)
            .ToDictionary(g => g.Key, g => g.First().DrawApproval);

        var paymentResponseList = new List<PaymentResponse>();

        var drawIds = paymentRequests
            .Select(x => x.PaymentRequestDetails?.DrawId.ToString())
            .Where(x => !string.IsNullOrEmpty(x))
            .Distinct()
            .ToArray();

        var loans = drawIds.Any() ? (await loanApiProxy.GetDrawsByIds(drawIds, cancellationToken)).Result : [];

        foreach (var paymentRequest in paymentRequests.Where(paymentRequest => paymentRequest.Status != PaymentRequestStatus.Cancelled))
        {
            companiesDict.TryGetValue(paymentRequest.PayerId ?? "", out var customerCompany);
            companiesDict.TryGetValue(paymentRequest.SellerId ?? "", out var sellerCompany);

            if (sellerCompany == null && !string.IsNullOrEmpty(paymentRequest.PayeeId))
            {
                companiesDict.TryGetValue(paymentRequest.PayeeId, out sellerCompany);
            }

            DateTime? processedAt = null;
            if (paymentRequest.Status == PaymentRequestStatus.Settled)
            {
                processedAt = paymentRequest.Transactions.MaxBy(x => x.SequenceNumber)?.ExecutedAt;
            }

            var investorType = OBS.Enums.DebtInvestorType.Arcadia;

            if (invoiceToDrawApprovalDict != null)
            {
                var invoiceIds = paymentRequest
                    .PaymentRequestPayables
                    .Select(x => x.PayableId)
                    .Where(x => !string.IsNullOrEmpty(x))
                    .Distinct()
                    .ToList();

                var targetDrawApprovals = invoiceIds
                    .Select(id => invoiceToDrawApprovalDict.TryGetValue(id, out var drawApp) ? drawApp : null)
                    .Where(x => x != null)
                    .ToList();

                if (targetDrawApprovals.Count != 0)
                {
                    var first = targetDrawApprovals[0];
                    investorType = first?.DebtInvestor ?? OBS.Enums.DebtInvestorType.Arcadia;
                }
            }

            var depositDetails = customerCompany?.Settings?.DepositDetails;
            var isSecured = depositDetails?.IsSecured;
            string? depositStatus = null;

            if (paymentRequest.RequestType is PaymentRequestType.DrawDisbursement
                or PaymentRequestType.FinalPayment
                or PaymentRequestType.FinalPaymentV2)
            {
                if (isSecured == true)
                {
                    depositStatus = depositDetails?.IsDepositPaid == true ? DepositStatusConstants.DepositStatusPaid : DepositStatusConstants.DepositStatusUnpaid;
                }
                else
                {
                    depositStatus = DepositStatusConstants.DepositStatusNotRequired;
                }
            }

            paymentResponseList.Add(new PaymentResponse
            {
                Id = paymentRequest.Id,
                DrawId = paymentRequest.PaymentRequestDetails?.DrawId,
                SellerId = paymentRequest.SellerId ?? paymentRequest.PayeeId,
                SellerName = sellerCompany?.Name,
                CustomerId = paymentRequest.PayerId,
                CustomerName = customerCompany?.Name,
                Type = paymentRequest.FlowTemplateCode,
                ApprovedDate = paymentRequest.Date,
                ReceivablesAmount = paymentRequest.PaymentRequestPayables?.Sum(x => x.Amount) ?? 0,
                AmountToDisburse = paymentRequest.Amount,
                ProcessedAt = processedAt,
                Status = paymentRequest.Status.ToString(),
                DisbursementConfirmed = paymentRequest.ConfirmedAt,
                InvestorType = investorType.ToString(),
                IsSecured = isSecured,
                DepositStatus = depositStatus,
                DownPaymentStatus = loans.FirstOrDefault(loan => loan.Id.ToString() == paymentRequest.PaymentRequestDetails?.DrawId.ToString())
                    ?.DownPaymentStatus ?? DownPaymentStatus.NotRequired,
                PaymentMethod = paymentRequest.PaymentMethod,
            });
        }

        return new GetQueryWithPaginationResultDto<PaymentResponse>
        {
            Result = paymentResponseList,
            TotalCount = result.TotalCount,
            PageNumber = result.PageNumber,
            PagesCount = result.PagesCount
        };
    }

    public Task<string?> GetAvailablePaymentMethods(CancellationToken cancellationToken)
    {
        var enabledDisbursementPaymentMethodsString = configuration[KeyVaultKeys.EnabledDisbursementPaymentMethods];
        if (string.IsNullOrEmpty(enabledDisbursementPaymentMethodsString))
        {
            return Task.FromResult<string?>(null);
        }

        var enabledDisbursementPaymentMethods = enabledDisbursementPaymentMethodsString.Split(';');

        var enabledPaymentMethods = Enum.GetValues<PaymentMethod>().ToDictionary(
            key => key.ToString(),
            v => enabledDisbursementPaymentMethods.Any(m => m.ToLower() == v.ToString().ToLower()));

        return Task.FromResult<string?>(JsonSerializer.Serialize(enabledPaymentMethods));
    }

    [ExcludeFromCodeCoverage]
    public List<PaymentMethodDto> GetManualPaymentMethods(string userId, CancellationToken ct)
    {
        var paymentMethods = new List<PaymentMethodDto>
        {
            new() { Name = "Incoming wire from customer", Method = ManualPaymentMethod.Wire.ToString() },
            new() { Name = "Check from customer", Method = ManualPaymentMethod.Check.ToString() },
            new() { Name = "Incoming ACH from customer", Method = ManualPaymentMethod.Ach.ToString() },
            new() { Name = "DACA Sweep", Method = ManualPaymentMethod.Daca.ToString() }
        };

        return paymentMethods;
    }

    public async Task ApprovePaymentRequest(Guid id, ApprovePaymentRequestModel request, string userId, CancellationToken ct)
    {
        ArgumentNullException.ThrowIfNull(id);
        ArgumentNullException.ThrowIfNull(request.DrawId);
        ArgumentNullException.ThrowIfNull(request.DrawId);

        await paymentProxy.ApprovePaymentRequest(id, request, userId, ct);

        var updateLoanDetails = new UpdateLoanDto
        {
            Status = InputLoanStatus.Started
        };
        await loanApiProxy.ChangeLoanStatus(request.DrawId, userId, updateLoanDetails, ct);
    }

    public async Task<DrawRepaymentManualRequestMessage> SendManualInternalPaymentRequestMessage(CreateManualPaymentRequest request, string userId, CancellationToken ct)
    {
        ArgumentNullException.ThrowIfNull(request);

        var loanId = Guid.TryParse(request.DrawId, out var loanGuid) ? loanGuid : Guid.NewGuid();

        var loan = await loanApiProxy.GetDrawById(loanId, ct);

        if (loan is null) throw new ValidationException(nameof(loan));

        var message = new DrawRepaymentManualRequestMessage()
        {
            FlowTemplateCode = FlowTemplateCodes.DrawRepaymentManual,
            CreatedBy = userId,
            BlueTapeCorrelationId = Guid.NewGuid().ToString(),
            PaymentRequestDetails = new()
            {
                Currency = request.Currency ?? "USD",
                RequestedAmount = request.Amount,
                PaymentMethod = "ach",
                PayablesDetails = new(),
                DrawDetails = new()
                {
                    Id = loanId,
                },
                CustomerDetails = new CustomerDetails
                {
                    Id = loan.CompanyId ?? string.Empty,
                },
                ManualPaymentDetails = new ManualPaymentDetails
                {
                    ManualPaymentMethod = request.ManualPaymentMethod,
                    ManualAccountCode = request.AccountCode,
                    ExternalReferenceNumber = request.ExternalReferenceNumber ?? string.Empty,
                    ExternalTransactionAmount = request.ExternalTransactionAmount,
                    UserId = userId,
                    UserName = !string.IsNullOrEmpty(userId) ? await DecisionEngineExtensions.ParseLastStatusChangedBy(userId, firebaseClient, ct) : null,
                },
            }
        };

        var payables = loan.LoanPayables?.Select(x => new PayablesDetail
        {
            Id = x.PayableId,
            PayableAmount = x.Amount ?? 0,
        }).ToList();

        if (payables is { Count: > 0 })
            message.PaymentRequestDetails.PayablesDetails.AddRange(payables);

        await loanApiProxy.CreateChangeLog(new()
        {
            LoanId = loanId,
            Note = "Manual Pull is added",
            UserId = userId,
            ChangeAt = dateProvider.CurrentDateTime.Date,
            PrimaryKeyValue = loanId.ToString(),
            EntityName = "ManualPayment",
            NewValue = request.Amount.ToString()
        }, userId, ct);

        await drawRepaymentManualMessageSender.SendMessage(new ServiceBusMessageBt<DrawRepaymentManualRequestMessage>(message), ct);

        return message;
    }

    public async Task CreateManualPull(CreateManualPull createManualPull, string userId, CancellationToken ct)
    {
        ArgumentNullException.ThrowIfNull(createManualPull);

        var loan = await loanApiProxy.GetDrawById(createManualPull.DrawId, ct);
        var bankAccount = await bankAccountsService.GetByBankAccountId(createManualPull.AccountId, ct);

        ValidateManualPullRequest(createManualPull, loan, bankAccount);

        var company = await companyApiProxy.GetCompanyById(loan!.CompanyId!, ct);
        if (company == null)
            throw new ArgumentException("Company cannot be null.");


        await loanApiProxy.CreateChangeLog(new CreateChangeLogDto
        {
            LoanId = loan.Id,
            Note = "Manual Pull is added",
            UserId = userId,
            ChangeAt = dateProvider.CurrentDateTime.Date,
            PrimaryKeyValue = loan.Id.ToString(),
            EntityName = "ManualPayment",
            NewValue = createManualPull.Amount.ToString("0.00"),
        }, userId, ct);

        if (loan.ActiveLoanTemplate!.Product == ProductType.LineOfCredit)
        {
            if (bankAccount!.PaymentMethodType == "card")
            {
                await CreateCardPaymentRequestMessage(createManualPull, userId, loan, bankAccount, ct);
            }
            else
            {
                await SentDrawRepaymentRequestMessage(createManualPull, userId, loan, bankAccount!, company, ct);
            }
        }
        else
        {
            await CreatePayNowPaymentRequestMessage(createManualPull, userId, loan, bankAccount!, company, ct);
        }
    }

    private static readonly LoanStatus[] PullValidLoanStatuses = [LoanStatus.Started, LoanStatus.Pending, LoanStatus.Recovered, LoanStatus.Created];

    private static void ValidateManualPullRequest(CreateManualPull createManualPull, LoanDto? loan, BankAccountModelWithDecodedAccountNumber? bankAccount)
    {
        if (loan is null) throw new ValidationException(nameof(loan));

        if (!PullValidLoanStatuses.Contains(loan.Status))
            throw new ArgumentException("Draw status is not created or started or pending.");

        if (loan.ActiveLoanTemplate == null)
            throw new ArgumentException("ActiveLoanTemplate cannot be null.");

        if (loan.CompanyId == null)
            throw new ArgumentException("Loan CompanyId cannot be null.");

        if (bankAccount == null || bankAccount.IsDeactivated == true)
            throw new ArgumentException("Bank account is deactivated.");

        if ((loan.LoanDetails?.LoanOutstandingAmount ?? 0.0m) + Tolerance < createManualPull.Amount)
            throw new ArgumentException("Requested amount is greater than outstanding amount.");
    }

    private async Task CreateCardPaymentRequestMessage(CreateManualPull createManualPull, string userId,
        LoanDto loan, BankAccountModelWithDecodedAccountNumber bankAccount, CancellationToken ct)
    {
        var message = new CardPaymentRequest
        {
            DrawId = loan.Id.ToString(),
            Method = "bankAccount",
            Amount = createManualPull.Amount,
            BankAccountId = bankAccount.Id
        };

        var response = await nodeService.ProcessCardPayment(message, userId, ct);
        if (response is null)
            throw new InvalidOperationException("Failed to process card payment");
        if (!response.Success)
            throw new InvalidOperationException(response.Result);
    }

    public async Task CreateSubscriptionPayment(CreateSubscriptionPaymentRequest request, string userId, CancellationToken ct)
    {
        ArgumentNullException.ThrowIfNull(request);
        ArgumentNullException.ThrowIfNull(userId);

        // Validate company exists
        var company = await companyApiProxy.GetCompanyById(request.CompanyId, ct);
        if (company == null)
            throw new ArgumentException("Company not found");

        // Validate bank account exists and is active
        var bankAccount = await bankAccountsService.GetByBankAccountId(request.AccountId, ct);
        if (bankAccount == null)
            throw new ArgumentException("Bank account not found");

        if (bankAccount is { IsDeactivated: true })
            throw new ArgumentException("Bank account is deactivated");

        // Create a subscription payment message
        var message = new SubscriptionPaymentRequestMessage
        {
            FlowTemplateCode = FlowTemplateCodes.SubscriptionFeePayment,
            CreatedBy = userId,
            BlueTapeCorrelationId = $"BFF-{Guid.NewGuid()}",
            PaymentRequestDetails = new SubscriptionPaymentRequestDetails
            {
                Date = DateTime.Now,
                Currency = request.Currency,
                RequestedAmount = request.Amount,
                PaymentMethod = "ach",
                ConfirmationType = ConfirmationType.None,
                CustomerDetails = new CustomerDetails
                {
                    Id = company.Id,
                    Name = !string.IsNullOrWhiteSpace(company.Name) ? company.Name : company.LegalName,
                    AccountId = bankAccount.Id
                },

                AdditionalDetails = new AdditionalDetails
                {
                    InvoiceNumber = request.InvoiceNumber,
                    Reason = request.Reason
                }
            }
        };

        // Send a message to queue
        await subscriptionPaymentMessageSender.SendMessage(new ServiceBusMessageBt<SubscriptionPaymentRequestMessage>(message), ct);
    }

    private async Task SentDrawRepaymentRequestMessage(CreateManualPull createManualPull, string userId,
        LoanDto loan, BankAccountModel bankAccount, CompanyModel company, CancellationToken ct)
    {
        var message = new DrawRepaymentRequestMessage()
        {
            FlowTemplateCode = FlowTemplateCodes.DrawRepayment,
            CreatedBy = userId,
            BlueTapeCorrelationId = "BFF-" + Guid.NewGuid(),
            PaymentRequestDetails = new DrawRepaymentRequestDetails()
            {
                Date = DateTime.Now,
                Currency = createManualPull.Currency,
                RequestedAmount = createManualPull.Amount,
                PaymentMethod = "ach",
                DrawRepaymentDrawDetails = new DrawRepaymentDrawDetails()
                {
                    Id = loan.Id,
                    Amount = loan.Amount
                },
                CustomerDetails = new CustomerDetails
                {
                    Id = company.Id,
                    Name = string.IsNullOrWhiteSpace(company.Name) ? company.LegalName : company.Name,
                    AccountId = bankAccount.Id,
                },
                ManualPaymentDetails = new ManualPaymentDetails()
                {
                    UserId = userId,
                    UserName = !string.IsNullOrEmpty(userId) ? await DecisionEngineExtensions.ParseLastStatusChangedBy(userId, firebaseClient, ct) : null,
                }
            }
        };

        await drawRepaymentMessageSender.SendMessage(new ServiceBusMessageBt<DrawRepaymentRequestMessage>(message),
            ct);
    }

    private async Task CreatePayNowPaymentRequestMessage(CreateManualPull createManualPull, string userId, LoanDto loan, BankAccountModel bankAccount, CompanyModel company, CancellationToken ct)
    {
        var message = new InvoicePaymentRequestMessage
        {
            FlowTemplateCode = FlowTemplateCodes.InvoicePaymentV2,
            CreatedBy = userId,
            BlueTapeCorrelationId = "BFF-" + Guid.NewGuid().ToString(),
            PaymentRequestDetails = new InvoicePaymentRequestDetails
            {
                Date = DateTime.Now,
                Currency = createManualPull.Currency,
                RequestedAmount = createManualPull.Amount,
                PaymentMethod = "ach",
                CustomerDetails = new CustomerDetails
                {
                    Id = company.Id,
                    Name = !string.IsNullOrWhiteSpace(company.Name) ? company.Name : company.LegalName,
                    AccountId = bankAccount.Id
                },
                SellerDetails = new SellerDetails
                {
                    CompanyId = loan.MerchantId ?? string.Empty,
                    Name = loan.MerchantName ?? string.Empty,
                },
                ManualPaymentDetails = new ManualPaymentDetails()
                {
                    UserId = userId,
                    UserName = !string.IsNullOrEmpty(userId) ? await DecisionEngineExtensions.ParseLastStatusChangedBy(userId, firebaseClient, ct) : null,
                }
            }
        };

        foreach (var invoice in loan.LoanPayables)
        {
            message.PaymentRequestDetails!.PayablesDetails!.Add(new()
            {
                Id = invoice.PayableId,
            });
        }

        await invoicePaymentMessageSender.SendMessage(new ServiceBusMessageBt<InvoicePaymentRequestMessage>(message), ct);
    }

    public async Task<GetQueryWithPaginationResultDto<PaymentsSubscriptionResponse>> GetSubscriptions(
        SubscriptionPaymentsQuery query,
        CancellationToken cancellationToken)
    {
        ArgumentNullException.ThrowIfNull(query);
        var filterQuery = new PaymentRequestFilterQuery
        {
            ApprovedFrom = query.From,
            ApprovedTo = query.To,
            SortBy = query.SortBy,
            SortOrder = query.SortOrder,
            PageNumber = query.PageNumber,
            PageSize = query.PageSize,
            FlowTemplateCodes = [FlowTemplateCodes.SubscriptionFeePayment]
        };

        if (!string.IsNullOrWhiteSpace(query.Search))
        {
            //take all, filter here
            filterQuery.PageSize = int.MaxValue;
            filterQuery.PageNumber = 1;
        }

        var result = await paymentProxy.GetPaymentRequests(filterQuery, cancellationToken);
        if (result?.Result is null || result.TotalCount == 0 || !result.Result.Any())
            return new GetQueryWithPaginationResultDto<PaymentsSubscriptionResponse>
            {
                Result = [],
                PageNumber = query.PageNumber,
                PagesCount = 0,
                TotalCount = 0
            };

        var paymentRequests = result.Result.ToArray();
        var finalResultsCount = result.TotalCount;
        var finalPagesCount = CalculatePagesCount(finalResultsCount, query.PageSize);

        if (!string.IsNullOrWhiteSpace(query.Search))
        {
            var companiesByName = await companyApiProxy.GetCompaniesList(new CompanyQueryPaginated
            {
                Name = query.Search,
                PageSize = int.MaxValue,
                PageNumber = 1
            }, cancellationToken);

            var filteredCompanyIds = companiesByName.Result.Select(c => c.Id).ToHashSet(StringComparer.OrdinalIgnoreCase);

            var paymentRequestQuery = paymentRequests
                .Where(p => (p.PayerId != null && filteredCompanyIds.Contains(p.PayerId)) ||
                            p.SellerId != null && filteredCompanyIds.Contains(p.SellerId))
                .Union(paymentRequests.Where(p => p.PaymentRequestDetails?.InvoiceNumber != null
                                                  && p.PaymentRequestDetails!.InvoiceNumber
                                                      .Contains(query.Search, StringComparison.OrdinalIgnoreCase)));

            paymentRequestQuery = query.SortOrder == "desc"
                ? paymentRequestQuery.OrderByDescending(p => p.CreatedAt ?? p.Date.ToDateTime(new TimeOnly(0))).ToList()
                : paymentRequestQuery.OrderBy(p => p.CreatedAt ?? p.Date.ToDateTime(new TimeOnly(0))).ToList();

            finalResultsCount = paymentRequestQuery.Count();
            finalPagesCount = (long)Math.Ceiling((decimal)finalResultsCount / query.PageSize);
            paymentRequests = paymentRequestQuery
                .Skip((query.PageNumber - 1) * query.PageSize)
                .Take(query.PageSize)
                .ToArray();
        }

        var companiesDict = await CompileCompanyIdListFromRequests(paymentRequests, cancellationToken);

        var subscriptionResponses = GeneratePaymentSubscriptionResponse(paymentRequests, companiesDict);

        return new GetQueryWithPaginationResultDto<PaymentsSubscriptionResponse>
        {
            PageNumber = result.PageNumber,
            // Override the default PagesCount with finalPagesCount to account for the adjusted pagination logic
            // when filters (e.g., search) are applied. This ensures the pagination reflects the filtered results.
            PagesCount = finalPagesCount,
            TotalCount = finalResultsCount,
            Result = subscriptionResponses
        };
    }

    private static long CalculatePagesCount(long finalResultsCount, int queryPageSize)
    {
        return (long)Math.Ceiling((decimal)finalResultsCount / queryPageSize);
    }

    public async Task<FileStreamResult> ExportSubscriptions(SubscriptionPaymentsQuery query, CancellationToken cancellationToken)
    {
        ArgumentNullException.ThrowIfNull(query);

        // Get all subscription fees without pagination
        var filterQuery = new PaymentRequestFilterQuery
        {
            ApprovedFrom = query.From,
            ApprovedTo = query.To,
            SortBy = query.SortBy,
            SortOrder = query.SortOrder,
            PageNumber = query.PageNumber,
            PageSize = query.PageSize,
            FlowTemplateCodes = [FlowTemplateCodes.SubscriptionFeePayment]
        };

        var result = await paymentProxy.GetPaymentRequests(filterQuery, cancellationToken);
        if (result?.Result is null || result.TotalCount == 0 || !result.Result.Any())
            return await reportFileGenerator.GenerateReportFileStreamResult(new List<SubscriptionFeeExportRecord>(), ReportType.Xlsx, GetExportFileName(query));

        var paymentRequests = result.Result.ToArray();

        var companiesDict = await CompileCompanyIdListFromRequests(paymentRequests, cancellationToken);

        // Generate subscription responses
        var subscriptionResponses = GeneratePaymentSubscriptionResponse(paymentRequests, companiesDict).ToList();

        // Map to export records with display attributes using AutoMapper
        var exportRecords = mapper.Map<List<SubscriptionFeeExportRecord>>(subscriptionResponses);

        // Generate Excel file
        return await reportFileGenerator.GenerateReportFileStreamResult(exportRecords, ReportType.Xlsx, GetExportFileName(query));
    }

    private static string GetExportFileName(SubscriptionPaymentsQuery query)
    {
        var fromDate = query.From?.ToString("yyyy-MM-dd") ?? "all";
        var toDate = query.To?.ToString("yyyy-MM-dd") ?? "all";
        return $"subscription_fees_{fromDate}_{toDate}";
    }

    private async Task<Dictionary<string, CompanyModel>> CompileCompanyIdListFromRequests(PaymentRequestModel[] paymentRequests,
        CancellationToken cancellationToken)
    {
        var sellerIds = paymentRequests
            .Where(r => !string.IsNullOrEmpty(r.SellerId))
            .Select(r => r.SellerId);

        var payerIds = paymentRequests
            .Where(r => !string.IsNullOrEmpty(r.PayerId))
            .Select(r => r.PayerId);

        var allCompanyIds = sellerIds
            .Union(payerIds)
            .Where(x => !string.IsNullOrEmpty(x))
            .Select(x => x!)
            .Distinct()
            .ToList();

        var companiesList = await companyApiProxy.GetCompaniesByIds(allCompanyIds.ToArray(), cancellationToken);
        var companiesDict = companiesList
            .ToDictionary(c => c.Id, c => c, StringComparer.OrdinalIgnoreCase);
        return companiesDict;
    }

    private static IEnumerable<PaymentsSubscriptionResponse> GeneratePaymentSubscriptionResponse(PaymentRequestModel[] paymentRequests,
        Dictionary<string, CompanyModel> companiesDict)
    {
        var subscriptionResponses = paymentRequests
            .Select(p =>
            {
                companiesDict.TryGetValue(p.PayerId ?? p.SellerId ?? "", out var customerCompany);

                return new PaymentsSubscriptionResponse
                {
                    Id = p.Id.ToString(),
                    CreatedAt = p.CreatedAt ?? p.Date.ToDateTime(new TimeOnly(0)),
                    CreatedBy = p.CreatedBy,
                    CompanyId = p.PayerId ?? p.SellerId,
                    CompanyName = customerCompany?.Name ?? string.Empty,
                    Amount = p.Amount,
                    Status = p.Status,
                    InvoiceNumber = p.PaymentRequestDetails?.InvoiceNumber,
                    Reason = p.PaymentRequestDetails?.Reason,
                    TransactionNumber = p.Transactions.FirstOrDefault()?.TransactionNumber,
                };
            });
        return subscriptionResponses;
    }
}
