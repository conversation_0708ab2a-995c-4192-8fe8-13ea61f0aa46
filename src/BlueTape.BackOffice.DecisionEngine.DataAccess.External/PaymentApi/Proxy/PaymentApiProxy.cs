using BlueTape.BackOffice.DecisionEngine.DataAccess.External.Constants;
using BlueTape.BackOffice.DecisionEngine.DataAccess.External.DI.Constants;
using BlueTape.BackOffice.DecisionEngine.DataAccess.External.PaymentApi.HttpClients;
using BlueTape.BackOffice.DecisionEngine.Domain.Models;
using BlueTape.Integrations.Aion.Transactions;
using BlueTape.OBS.DTOs;
using BlueTape.PaymentService.Models;
using BlueTape.Utilities.Models;
using Microsoft.Extensions.Configuration;
using System.Web;

namespace BlueTape.BackOffice.DecisionEngine.DataAccess.External.PaymentApi.Proxy;

public class PaymentApiProxy(
    IPaymentHttpClient httpClient,
    IConfiguration configuration)
    : IPaymentApiProxy
{
    public Task ApprovePaymentRequest(Guid id, ApprovePaymentRequestModel request, string userId, CancellationToken cancellationToken)
    {
        var path = $"{PaymentServiceConstants.Payment}/{id}/approve";

        var headers = new Dictionary<string, string>() {
            { "CreatedBy", userId }
        }.AsReadOnly();

        return httpClient.PatchAsync<object>(path, request, headers, cancellationToken);
    }

    public async Task<PaymentRequestModel?> GetById(Guid id, CancellationToken cancellationToken)
    {
        var path = $"{PaymentServiceConstants.Payment}/{id}";

        return await httpClient.Get<PaymentRequestModel>(path, cancellationToken);
    }

    public Task<List<AionAccount?>?> GetAllAccounts(string? paymentProvider, CancellationToken cancellationToken)
    {
        var path = $"{PaymentServiceConstants.Payment}/accounts?paymentProvider={paymentProvider}";

        return httpClient.Get<List<AionAccount?>>(path, cancellationToken);
    }

    public async Task<List<AionAccountShort?>?> GetAccounts(string? paymentProvider, CancellationToken cancellationToken)
    {
        var aionResults = await GetAllAccounts(paymentProvider, cancellationToken);

        var fundingId = configuration[KeyVaultKeys.FundingAccountId];
        var collectionId = configuration[KeyVaultKeys.CollectionAccountId];

        var shortResult = new List<AionAccountShort?>();

        var fundingAccount = aionResults?.FirstOrDefault(x => x?.Id == fundingId);
        var collectionAccount = aionResults?.FirstOrDefault(x => x?.Id == collectionId);

        if (fundingAccount != null)
        {
            shortResult.Add(new AionAccountShort()
            {
                AvailableBalance = fundingAccount.AvailableBalance,
                Name = fundingAccount.Name,
                AmountOnHold = fundingAccount.AmountOnHold,
                Type = "Funding"
            });
        }

        if (collectionAccount != null)
        {
            shortResult.Add(new AionAccountShort()
            {
                AvailableBalance = collectionAccount.AvailableBalance,
                Name = collectionAccount.Name,
                AmountOnHold = collectionAccount.AmountOnHold,
                Type = "Collection"
            });
        }

        return shortResult;
    }

    public async Task<GetQueryWithPaginationResultDto<PaymentRequestModel>?> GetPaymentRequests(PaymentRequestFilterQuery query, CancellationToken cancellationToken)
    {
        var path = $"{PaymentServiceConstants.Payment}";
        var queryString = HttpUtility.ParseQueryString(string.Empty);

        if (query is { DrawId: not null })
            queryString["drawId"] = query.DrawId;
        if (query is { Filter: not null })
            queryString["filter"] = query.Filter;

        if (query.FlowTemplateCodes != null)
        {
            foreach (var type in query.FlowTemplateCodes)
            {
                queryString.Add("flowTemplateCodes", type);
            }
        }

        if (query.Status != null)
        {
            foreach (var status in query.Status)
            {
                queryString.Add("paymentRequestStatuses", status);
            }
        }

        if (query.ApprovedFrom.HasValue)
            queryString["From"] = query.ApprovedFrom.Value.ToString("yyyy-MM-dd");
        if (query.ApprovedTo.HasValue)
            queryString["To"] = query.ApprovedTo.Value.ToString("yyyy-MM-dd");
        if (query.IsConfirmed.HasValue)
            queryString["isConfirmed"] = query.IsConfirmed.Value.ToString();
        if (query.SortOrder != null)
            queryString["sortOrder"] = query.SortOrder;
        if (query.SortBy != null)
            queryString["sortBy"] = query.SortBy;
        if (query.PageNumber.HasValue)
            queryString["page"] = query.PageNumber.Value.ToString();
        if (query.PageSize.HasValue)
            queryString["items"] = query.PageSize.Value.ToString();

        var fullPath = $"{path}?{queryString}";

        return await httpClient.Get<GetQueryWithPaginationResultDto<PaymentRequestModel>>(fullPath, cancellationToken);
    }

    public async Task<PaginatedResponse<TransactionListObj>?> GetAccountTransactions(TransactionsQuery request, CancellationToken ct)
    {
        var queryParams = new Dictionary<string, string>
        {
            ["pageNumber"] = request.PageNumber.ToString(),
            ["pageSize"] = request.PageSize.ToString(),
            ["accountCodeType"] = request.AccountCodeType.ToString()
        };

        var queryString = string.Join("&", queryParams
            .Where(p => !string.IsNullOrEmpty(p.Value))
            .Select(p => $"{p.Key}={Uri.EscapeDataString(p.Value)}"));

        var path = $"{PaymentServiceConstants.Transactions}/accountCode?{queryString}";

        var result = await httpClient.Get<PaginatedResponse<TransactionListObj>>(path, ct);

        return result;
    }

    public async Task CancelPaymentByDrawId(Guid drawId, string userId, CancellationToken ct)
    {
        var path = $"{PaymentServiceConstants.Payment}/draw/{drawId}?templateCode=CREATE.DRAW.DISBURSEMENT";
        var headers = new Dictionary<string, string>() {
            { "userId", userId }
        }.AsReadOnly();

        await httpClient.DeleteAsync(path, payload: string.Empty, headers, ct);
    }
}
