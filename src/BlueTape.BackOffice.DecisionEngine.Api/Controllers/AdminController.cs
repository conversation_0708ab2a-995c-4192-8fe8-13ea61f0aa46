using AutoMapper;
using BlueTape.BackOffice.DecisionEngine.Api.Attributes;
using BlueTape.BackOffice.DecisionEngine.Api.Constants;
using BlueTape.BackOffice.DecisionEngine.Api.Models.Error;
using BlueTape.BackOffice.DecisionEngine.Api.Models.Requests;
using BlueTape.BackOffice.DecisionEngine.Api.Models.Responses.DrawApprovals;
using BlueTape.BackOffice.DecisionEngine.Api.Models.Responses.DrawApprovals.Details;
using BlueTape.BackOffice.DecisionEngine.Api.Models.Responses.Users;
using BlueTape.BackOffice.DecisionEngine.Application.Services.Accounts.Models;
using BlueTape.BackOffice.DecisionEngine.Application.Services.Admin;
using BlueTape.BackOffice.DecisionEngine.Application.Services.Admin.Models;
using BlueTape.BackOffice.DecisionEngine.Application.Services.DrawApprovals.Models;
using BlueTape.BackOffice.DecisionEngine.Domain.Extensions;
using BlueTape.BackOffice.DecisionEngine.Domain.Models;
using BlueTape.CompanyService.BankAccounts;
using BlueTape.CompanyService.CashFlow;
using BlueTape.CompanyService.CashFlow.Responses;
using BlueTape.CompanyService.Companies;
using BlueTape.Firebase.Abstractions;
using BlueTape.LS.DTOs.CardPricingPackages;
using BlueTape.LS.DTOs.Credit;
using BlueTape.LS.DTOs.LoanPricingPackages;
using BlueTape.OBS.DTOs.DrawApproval.Requests;
using FluentValidation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Diagnostics.CodeAnalysis;
using System.Net;
using System.Security.Claims;

namespace BlueTape.BackOffice.DecisionEngine.Api.Controllers
{
    [ExcludeFromCodeCoverage]
    [ApiController]
    [Authorize(Roles = Roles.Admin)]
    [Route(Routes.AdminFunctions)]
    public class AdminController(IAdminService adminService, IValidator<IFormFile> cashFlowFileValidator, IMapper mapper, IFirebaseClient firebaseClient) : ControllerBase
    {
        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.BadRequest)]
        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.NotFound)]
        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.InternalServerError)]
        [ProducesResponseType(typeof(CreditDto), (int)HttpStatusCode.OK)]
        [HttpPatch("credits/{id}/details")]
        public async Task<CreditDto> UpdateCreditDetails([FromRoute] Guid id, UpdateCreditDetailsDto updateCreditDto, CancellationToken ct)
        {
            var userId = this.User
                .FindFirst(claim => claim.Type == ClaimTypes.NameIdentifier)
                .EnsureExists(Messages.MissingUserIdClaimMessage).Value;
            var result = await adminService.UpdateCreditDetails(id, userId, updateCreditDto, ct);
            return result;
        }

        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.BadRequest)]
        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.NotFound)]
        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.InternalServerError)]
        [ProducesResponseType(typeof(AddManualCashFlow), (int)HttpStatusCode.OK)]
        [HttpPost("{companyId}/cashFlow/{accountId}/file")]
        public async Task<AddManualCashFlow> AddManualCashFlow(
            [FromRoute] string companyId,
            [FromRoute] string accountId,
            [FromForm] IFormFile cashFlowReport,
            CancellationToken ctx)
        {
            await cashFlowFileValidator.ValidateAndThrowAsync(cashFlowReport, ctx);

            var userId = User
                .FindFirst(claim => claim.Type == ClaimTypes.NameIdentifier)
                .EnsureExists(Messages.MissingUserIdClaimMessage).Value;
            var result = await adminService.AddManualCashFlow(companyId, accountId, userId, cashFlowReport, ctx);
            return result;

        }

        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.BadRequest)]
        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.NotFound)]
        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.InternalServerError)]
        [ProducesResponseType(typeof(ManualCashFlowValidationResultResponse), (int)HttpStatusCode.OK)]
        [HttpPost("cashFlow/validate-file")]
        public async Task<ManualCashFlowValidationResultResponse> ValidateManualCashFlow([FromForm] IFormFile cashFlowReport, CancellationToken ctx)
        {
            await cashFlowFileValidator.ValidateAndThrowAsync(cashFlowReport, ctx);

            var result = await adminService.ValidateManualCashFlow(cashFlowReport, ctx);
            return result;
        }

        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.BadRequest)]
        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.NotFound)]
        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.InternalServerError)]
        [ProducesResponseType((int)HttpStatusCode.OK)]
        [HttpPost("creditApplications/{creditApplicationId}/execute")]
        public Task RunDecisionEngineForCreditApplication([FromRoute][BsonId] string creditApplicationId, CancellationToken ctx)
        {
            return adminService.RunDecisionEngineForCreditApplication(creditApplicationId, ctx);
        }

        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.BadRequest)]
        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.NotFound)]
        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.InternalServerError)]
        [ProducesResponseType(typeof(StepFunctionsExecutionResponse), (int)HttpStatusCode.OK)]
        [HttpPost("creditApplications/{getPaidApplicationId}/runasaradvance")]
        public Task<StepFunctionsExecutionResponse> RunGetPaidApplicationAsArAdvance([FromRoute][BsonId] string getPaidApplicationId, CancellationToken ctx)
        {
            return adminService.RunGetPaidApplicationAsArAdvance(getPaidApplicationId, ctx);
        }

        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.BadRequest)]
        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.NotFound)]
        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.InternalServerError)]
        [ProducesResponseType((int)HttpStatusCode.OK)]
        [HttpPost("drawApprovals/{drawApprovalId}/execute")]
        public Task RunDecisionEngineForDrawApproval([FromRoute][BsonId] string drawApprovalId, CancellationToken ctx)
        {
            return adminService.RunDecisionEngineForDrawApproval(drawApprovalId, ctx);
        }

        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.BadRequest)]
        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.NotFound)]
        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.InternalServerError)]
        [ProducesResponseType(typeof(DrawApprovalDetailsRecordResponse), (int)HttpStatusCode.OK)]
        [HttpPost("drawApprovals/{drawApprovalId}/attachsupplier")]
        public async Task<DrawApprovalDetailsRecordResponse> AttachSupplierToDrawApprovals(
            [FromRoute][BsonId] string drawApprovalId,
            [FromBody] AttachSupplierRequest attachSupplierRequest,
            CancellationToken ctx)
        {
            var userId = User
                .FindFirst(claim => claim.Type == ClaimTypes.NameIdentifier)
                .EnsureExists(Messages.MissingUserIdClaimMessage).Value;

            var dto = mapper.Map<AttachSupplierToDrawApproval>(attachSupplierRequest);
            var drawApproval = await adminService.AttachSupplierToDrawApprovals(drawApprovalId, userId, dto, ctx);
            var result = mapper.Map<DrawApprovalDetailsRecordResponse>(drawApproval);
            return result;
        }

        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.BadRequest)]
        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.NotFound)]
        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.InternalServerError)]
        [ProducesResponseType(typeof(Uri), (int)HttpStatusCode.OK)]
        [HttpGet("AWS/preSignedUrl")]
        public async Task<Uri> GetAWSPreSignedUrl([FromQuery] string bucketReference, CancellationToken ctx)
        {
            var result = await adminService.GetAWSPreSignedUrl(bucketReference, ctx);
            return result;
        }

        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.BadRequest)]
        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.NotFound)]
        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.InternalServerError)]
        [ProducesResponseType(typeof(CompanyModel), (int)HttpStatusCode.OK)]
        [HttpPost($"{Routes.Companies}/{Routes.GuestSupplier}")]
        public async Task<CompanyModel?> PostGuestSupplier([FromBody] CreateUpdateGuestSupplierRequest dto, CancellationToken ct)
        {
            var userId = this.User
                .FindFirst(claim => claim.Type == ClaimTypes.NameIdentifier)
                .EnsureExists(Messages.MissingUserIdClaimMessage).Value;

            var result = await adminService.CreateGuestSupplier(userId, mapper.Map<CreateUpdateGuestSupplierModel>(dto), ct);
            return result;
        }

        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.BadRequest)]
        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.NotFound)]
        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.InternalServerError)]
        [ProducesResponseType(typeof(CompanyNoteModel), (int)HttpStatusCode.OK)]
        [HttpPost($"{Routes.Companies}/{Routes.GuestSupplier}/{Routes.Id}/{Routes.Notes}")]
        public async Task<CompanyNoteModel?> AddCompanyNote([FromRoute] string id,
            [FromBody] CreateCompanyNoteModel dto, CancellationToken ct)
        {
            var userId = this.User
                .FindFirst(claim => claim.Type == ClaimTypes.NameIdentifier)
                .EnsureExists(Messages.MissingUserIdClaimMessage).Value;

            var result = await adminService.CreateCompanyNote(id, userId, dto, ct);
            return result;
        }

        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.BadRequest)]
        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.NotFound)]
        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.InternalServerError)]
        [ProducesResponseType(typeof(CompanyModel), (int)HttpStatusCode.OK)]
        [HttpPatch($"{Routes.Companies}/{Routes.GuestSupplier}/{Routes.Id}")]
        public async Task<CompanyModel?> PatchGuestSupplier([FromRoute] string id,
            [FromBody] UpdateGuestSupplierRequest dto, CancellationToken ct)
        {
            var userId = this.User
                .FindFirst(claim => claim.Type == ClaimTypes.NameIdentifier)
                .EnsureExists(Messages.MissingUserIdClaimMessage).Value;

            var result = await adminService.UpdateGuestSupplier(id, userId, mapper.Map<CreateUpdateGuestSupplierModel>(dto), ct);
            return result;
        }

        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.BadRequest)]
        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.NotFound)]
        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.InternalServerError)]
        [ProducesResponseType((int)HttpStatusCode.OK)]
        [HttpPatch("bank-accounts/{id}/cashFlow")]
        public Task PatchCashFlowInclusion([FromRoute][BsonId] string id, [FromBody] PatchBankAccountCashFlow dto, CancellationToken ct)
            => adminService.PatchCashFlowInclusion(id, mapper.Map<PatchBankAccountCashFlowModel>(dto), ct);

        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.BadRequest)]
        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.NotFound)]
        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.InternalServerError)]
        [ProducesResponseType(typeof(BankAccountModel), (int)HttpStatusCode.OK)]
        [HttpPatch("bank-account/{id}")]
        public Task<BankAccountModel?> UpdateBankAccountNumberModel(
            [FromRoute][BsonId] string id,
            [FromBody] UpdateBankAccountNumberRequest dto,
            CancellationToken ct)
        {
            var userId = this.User
                    .FindFirst(claim => claim.Type == ClaimTypes.NameIdentifier)
                    .EnsureExists(Messages.MissingUserIdClaimMessage).Value;

            return adminService.UpdateBankAccountNumberModel(id, userId,
               mapper.Map<UpdateBankAccountNumberRequestModel>(dto), ct);
        }

        [HttpPost($"{Routes.DrawApprovals}/{Routes.Id}/{Routes.PostTransaction}")]
        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.BadRequest)]
        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.NotFound)]
        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.InternalServerError)]
        public async Task<DrawApprovalResponse> PostTransaction(
            [FromRoute] string id,
            [FromBody] NoteDto dto,
            CancellationToken ct)
            => mapper.Map<DrawApprovalResponse>(await adminService.PostTransaction(id, mapper.Map<NoteModel>(dto), ct));

        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.BadRequest)]
        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.NotFound)]
        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.InternalServerError)]
        [HttpPatch($"{Routes.DrawApprovals}/{Routes.Id}/{Routes.QuoteExpiration}")]
        public async Task<DrawApprovalResponse> QuoteExpiration(
            [FromRoute] string id,
            [FromBody] PatchExpirationDateRequest request,
            CancellationToken ct)
            => mapper.Map<DrawApprovalResponse>(await adminService.PatchQuoteExpiration(id, mapper.Map<PatchExpirationDateModel>(request), ct));

        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.BadRequest)]
        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.NotFound)]
        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.InternalServerError)]
        [HttpPatch($"{Routes.Draws}/{Routes.Id}/{Routes.Cancel}")]
        public async Task CancelDraw([FromRoute] Guid id, CancellationToken ct)
        {
            var userId = this.User
                .FindFirst(claim => claim.Type == ClaimTypes.NameIdentifier)
                .EnsureExists(Messages.MissingUserIdClaimMessage).Value;

            await adminService.CancelDraw(id, userId, ct);
        }

        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.BadRequest)]
        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.NotFound)]
        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.InternalServerError)]
        [ProducesResponseType(typeof(LoanPricingPackageDto), (int)HttpStatusCode.OK)]
        [HttpPatch($"{Routes.LoanPricingPackages}/{Routes.Id}")]
        public async Task<LoanPricingPackageDto> UpdateLoanPricingPackage([FromRoute] string id, [FromBody] ShortLoanPricingPackageDto loanPricingPackageUpdate, CancellationToken ct)
        {
            var userId = this.User
                .FindFirst(claim => claim.Type == ClaimTypes.NameIdentifier)
                .EnsureExists(Messages.MissingUserIdClaimMessage).Value;

            var pricingPackage = await adminService.UpdateLoanPricingPackages(loanPricingPackageUpdate, userId, id, ct);

            return mapper.Map<LoanPricingPackageDto>(pricingPackage);
        }

        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.BadRequest)]
        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.NotFound)]
        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.InternalServerError)]
        [ProducesResponseType(typeof(LoanPricingPackageDto), (int)HttpStatusCode.OK)]
        [HttpPost($"{Routes.LoanPricingPackages}")]
        public async Task<LoanPricingPackageDto> AddLoanPricingPackage([FromBody] ShortLoanPricingPackageDto loanPricingPackageAdd, CancellationToken ct)
        {

            var userId = this.User
                .FindFirst(claim => claim.Type == ClaimTypes.NameIdentifier)
                .EnsureExists(Messages.MissingUserIdClaimMessage).Value;

            var pricingPackage = await adminService.AddLoanPricingPackages(loanPricingPackageAdd, userId, ct);

            return mapper.Map<LoanPricingPackageDto>(pricingPackage);
        }

        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.BadRequest)]
        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.NotFound)]
        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.InternalServerError)]
        [ProducesResponseType(typeof(CardPricingPackageDto), (int)HttpStatusCode.OK)]

        [HttpPatch($"{Routes.CardPricingPackages}/{Routes.Id}")]
        public async Task<CardPricingPackageDto> UpdateCardPricingPackage([FromRoute] string id, [FromBody] ShortCardPricingPackageDto loanPricingPackageUpdate, CancellationToken ct)
        {

            var userId = this.User
                .FindFirst(claim => claim.Type == ClaimTypes.NameIdentifier)
                .EnsureExists(Messages.MissingUserIdClaimMessage).Value;

            var pricingPackage = await adminService.UpdateCardPricingPackages(loanPricingPackageUpdate, userId, id, ct);

            return mapper.Map<CardPricingPackageDto>(pricingPackage);
        }

        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.BadRequest)]
        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.NotFound)]
        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.InternalServerError)]
        [ProducesResponseType(typeof(CardPricingPackageDto), (int)HttpStatusCode.OK)]
        [HttpPost($"{Routes.CardPricingPackages}")]
        public async Task<CardPricingPackageDto> AddCardPricingPackage([FromBody] ShortCardPricingPackageDto loanPricingPackageAdd, CancellationToken ct)
        {

            var userId = this.User
                .FindFirst(claim => claim.Type == ClaimTypes.NameIdentifier)
                .EnsureExists(Messages.MissingUserIdClaimMessage).Value;

            var pricingPackage = await adminService.AddCardPricingPackages(loanPricingPackageAdd, userId, ct);

            return mapper.Map<CardPricingPackageDto>(pricingPackage);
        }

        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.NotFound)]
        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.InternalServerError)]
        [HttpGet($"{Routes.Companies}/{Routes.Id}/deadrs/status")]
        public Task<RefreshExecutionStatusResult> GetRefreshServiceExecutionStatus([FromRoute] string id, CancellationToken ct)
        {
            return adminService.GetRefreshExecutionStatus(id, ct);
        }

        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.BadRequest)]
        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.NotFound)]
        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.InternalServerError)]
        [HttpPatch($"{Routes.Companies}/{Routes.Id}/deadrs/lastExecutions/rule/reinstate")]
        public Task ReinstateDecisionEngineRule([FromRoute] string id,
            [FromQuery] string? identifier,
            [FromBody] ReinstateDecisionEngineRuleModel reinstateDecisionEngineRule,
            CancellationToken ct)
        {
            return adminService.ReinstateDecisionEngineRule(id, identifier, reinstateDecisionEngineRule, ct);
        }

        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.BadRequest)]
        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.NotFound)]
        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.InternalServerError)]
        [HttpPatch($"{Routes.Companies}/{Routes.Id}/deadrs/lastExecutions/rule")]
        public Task IgnoreDecisionEngineRule([FromRoute] string id,
            [FromQuery] string? identifier,
            [FromBody] IgnoreDecisionEngineRuleModel ignoreDecisionEngineRule,
            CancellationToken ct)
        {
            return adminService.IgnoreDecisionEngineRule(id, identifier, ignoreDecisionEngineRule, ct);
        }

        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.BadRequest)]
        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.NotFound)]
        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.InternalServerError)]
        [HttpPost($"{Routes.Companies}/{Routes.Id}/deadrs/execute")]
        public Task ExecuteRefreshService([FromRoute] string id, CancellationToken ct)
        {
            return adminService.ExecuteRefreshService(id, ct);
        }


        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.BadRequest)]
        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.NotFound)]
        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.InternalServerError)]
        [HttpPost($"{Routes.Companies}/{Routes.Id}/deadrs/clear")]
        public Task RerunCalculationsAfterOverride([FromRoute] string id, [FromBody] ManualRefreshRunRequest request, CancellationToken ct)
        {
            var model = mapper.Map<ManualRefreshRunModel>(request);
            return adminService.ClearManualOverrideAndRerunRefreshService(id, model, ct);
        }

        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.BadRequest)]
        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.NotFound)]
        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.InternalServerError)]
        [HttpGet($"{Routes.FireBase}/{Routes.Users}/{Routes.Id}")]
        public async Task<FireBaseUserResponse?> GetFireBaseUserByIds([FromRoute] string id, CancellationToken ct)
        {
            var users = await firebaseClient.GetUsers([id], ct);
            var result = users.FirstOrDefault();

            return mapper.Map<FireBaseUserResponse?>(result);
        }

        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.BadRequest)]
        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.NotFound)]
        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.InternalServerError)]
        [HttpGet("debtInvestors")]
        public IEnumerable<string> GetDebtInvestors(CancellationToken cancellationToken) => adminService.GetDebtInvestors();

        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.BadRequest)]
        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.NotFound)]
        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.InternalServerError)]
        [HttpPatch($"{Routes.Companies}/{Routes.GuestSupplier}/{Routes.Id}/debtInvestorTradeCredit")]
        public async Task<CompanyModel?> UpdateDebtInvestorTradeCredit([FromRoute] string id, [FromBody] NewDebtInvestorTradeCreditRequest newDebtInvestorTradeCreditRequest, CancellationToken ct)
        {
            var userId = this.User
                .FindFirst(claim => claim.Type == ClaimTypes.NameIdentifier)
                .EnsureExists(Messages.MissingUserIdClaimMessage).Value;

            var result = await adminService.PatchDefaultDebtInvestorTradeCredit(userId, id, newDebtInvestorTradeCreditRequest.DefaultDebtInvestorTradeCredit, ct);

            return result;
        }

        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.BadRequest)]
        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.NotFound)]
        [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.InternalServerError)]
        [ProducesResponseType(typeof(CreditDto), (int)HttpStatusCode.OK)]
        [HttpPost("companies/{id}/bank-accounts/manual")]
        public async Task<BankAccountModel> CreateManualBankAccount([FromRoute] string id, CreateManualBankAccountRequest createManualBankAccount, CancellationToken ct)
        {
            var userId = this.User
                .FindFirst(claim => claim.Type == ClaimTypes.NameIdentifier)
                .EnsureExists(Messages.MissingUserIdClaimMessage).Value;
            var createModel = mapper.Map<CreateManualBankAccountModel>(createManualBankAccount);
            var result = await adminService.CreateManualBankAccount(id, createModel, userId, ct);

            return result;
        }
    }
}
