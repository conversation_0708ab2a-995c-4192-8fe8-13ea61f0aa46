using BlueTape.BackOffice.DecisionEngine.Api.Constants;
using BlueTape.BackOffice.DecisionEngine.Api.Models.Error;
using BlueTape.BackOffice.DecisionEngine.Application.Services.Admin.Models.Payments;
using BlueTape.BackOffice.DecisionEngine.Application.Services.Payments;
using BlueTape.BackOffice.DecisionEngine.Application.Services.Payments.Models;
using BlueTape.BackOffice.DecisionEngine.Domain.Extensions;
using BlueTape.BackOffice.DecisionEngine.Domain.Models;
using BlueTape.Integrations.Aion.Infrastructure.Enums;
using FluentValidation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Net;
using System.Security.Claims;

namespace BlueTape.BackOffice.DecisionEngine.Api.Controllers;

[ApiController]
[Authorize(Roles = Roles.Admin)]
[Route(Routes.PaymentsAdmin)]
public class PaymentsAdminController(
    IBackofficePaymentService paymentService,
    IValidator<CreateSubscriptionPaymentRequest> subscriptionPaymentValidator) : ControllerBase
{
    [ProducesResponseType(typeof(void), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.BadRequest)]
    [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.NotFound)]
    [HttpPost("{id}/approve")]
    public Task ApprovePaymentRequest(
        [FromRoute] Guid id,
        [FromBody] ApprovePaymentRequestModel request,
        CancellationToken cancellationToken)
    {
        var userId = this.User
            .FindFirst(claim => claim.Type == ClaimTypes.NameIdentifier)
            .EnsureExists(Messages.MissingUserIdClaimMessage).Value;

        return paymentService.ApprovePaymentRequest(id, request, userId, cancellationToken);
    }

    [ProducesResponseType(typeof(void), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.BadRequest)]
    [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.NotFound)]
    [HttpGet("manualPayments/accounts")]
    public List<AionAccountShort> GetManualPayments(CancellationToken ct)
    {
        var userId = this.User
            .FindFirst(claim => claim.Type == ClaimTypes.NameIdentifier)
            .EnsureExists(Messages.MissingUserIdClaimMessage).Value;

        return paymentService.GetManualPaymentAccounts(userId, ct);
    }

    [ProducesResponseType(typeof(void), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.BadRequest)]
    [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.NotFound)]
    [HttpPost]
    public Task CreateManualPaymentRequest([FromBody] CreateManualPaymentRequest request, CancellationToken ct)
    {
        var userId = this.User
            .FindFirst(claim => claim.Type == ClaimTypes.NameIdentifier)
            .EnsureExists(Messages.MissingUserIdClaimMessage).Value;

        return paymentService.SendManualInternalPaymentRequestMessage(request, userId, ct);
    }

    [ProducesResponseType(typeof(void), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.BadRequest)]
    [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.NotFound)]
    [HttpGet("transactions/{accountCode}")]
    public Task<List<AionTransaction>?> GetAccountTransactions([FromRoute] AccountCodeType accountCode, CancellationToken ct)
    {
        _ = User
            .FindFirst(claim => claim.Type == ClaimTypes.NameIdentifier)
            .EnsureExists(Messages.MissingUserIdClaimMessage).Value;

        return paymentService.GetAccountTransactions(accountCode, ct);
    }

    [HttpGet("manualPayments/methods")]
    public List<PaymentMethodDto> GetPaymentMethods(CancellationToken ct)
    {
        var userId = User
            .FindFirst(claim => claim.Type == ClaimTypes.NameIdentifier)
            .EnsureExists(Messages.MissingUserIdClaimMessage).Value;

        return paymentService.GetManualPaymentMethods(userId, ct);
    }

    [HttpGet("accounts/{accountCode}")]
    public Task<AionAccount> GetAccountByTypeCode([FromRoute] AccountCodeType accountCode, CancellationToken ct)
    {
        _ = User
            .FindFirst(claim => claim.Type == ClaimTypes.NameIdentifier)
            .EnsureExists(Messages.MissingUserIdClaimMessage).Value;

        return paymentService.GetAccountByTypeCode(accountCode, ct);
    }

    [HttpPost("pull")]
    public Task CreateManualPull(CreateManualPull createManualPull, CancellationToken ct)
    {
        var userId = User.FindFirst(claim => claim.Type == ClaimTypes.NameIdentifier)
            .EnsureExists(Messages.MissingUserIdClaimMessage).Value;

        return paymentService.CreateManualPull(createManualPull, userId, ct);
    }

    [ProducesResponseType(typeof(void), StatusCodes.Status201Created)]
    [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.BadRequest)]
    [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.NotFound)]
    [HttpPost("subscription")]
    public async Task<IActionResult> CreateSubscriptionPayment([FromBody] CreateSubscriptionPaymentRequest request, CancellationToken ct)
    {
        await subscriptionPaymentValidator.ValidateAndThrowAsync(request, ct);

        var userId = User.FindFirst(claim => claim.Type == ClaimTypes.NameIdentifier)
            .EnsureExists(Messages.MissingUserIdClaimMessage).Value;

        await paymentService.CreateSubscriptionPayment(request, userId, ct);
        return StatusCode(StatusCodes.Status201Created);
    }
}
