using AutoMapper;
using BlueTape.BackOffice.DecisionEngine.Api.Models.Queries;
using BlueTape.BackOffice.DecisionEngine.Api.Models.Requests;
using BlueTape.BackOffice.DecisionEngine.Api.Models.Responses;
using BlueTape.BackOffice.DecisionEngine.Api.Models.Responses.Accounts;
using BlueTape.BackOffice.DecisionEngine.Api.Models.Responses.Configuration;
using BlueTape.BackOffice.DecisionEngine.Api.Models.Responses.Credits;
using BlueTape.BackOffice.DecisionEngine.Api.Models.Responses.Customers;
using BlueTape.BackOffice.DecisionEngine.Api.Models.Responses.DeExecutions;
using BlueTape.BackOffice.DecisionEngine.Api.Models.Responses.DocumentVerification;
using BlueTape.BackOffice.DecisionEngine.Api.Models.Responses.DrawApprovals;
using BlueTape.BackOffice.DecisionEngine.Api.Models.Responses.DrawApprovals.Details;
using BlueTape.BackOffice.DecisionEngine.Api.Models.Responses.Giact;
using BlueTape.BackOffice.DecisionEngine.Api.Models.Responses.Project;
using BlueTape.BackOffice.DecisionEngine.Api.Models.Responses.Users;
using BlueTape.BackOffice.DecisionEngine.Application.Extensions;
using BlueTape.BackOffice.DecisionEngine.Application.Services.Accounts.Models;
using BlueTape.BackOffice.DecisionEngine.Application.Services.Accounts.Models.List;
using BlueTape.BackOffice.DecisionEngine.Application.Services.Admin.Models;
using BlueTape.BackOffice.DecisionEngine.Application.Services.AppConfiguration.Models;
using BlueTape.BackOffice.DecisionEngine.Application.Services.Common.Models;
using BlueTape.BackOffice.DecisionEngine.Application.Services.CreditApplicationNotes.Models;
using BlueTape.BackOffice.DecisionEngine.Application.Services.CreditApplications.Models;
using BlueTape.BackOffice.DecisionEngine.Application.Services.CreditApplications.Models.DecisionEngineExecutions;
using BlueTape.BackOffice.DecisionEngine.Application.Services.CreditApplications.Models.DocumentVerification;
using BlueTape.BackOffice.DecisionEngine.Application.Services.DrawApprovalNotes.Models;
using BlueTape.BackOffice.DecisionEngine.Application.Services.DrawApprovals.Models;
using BlueTape.BackOffice.DecisionEngine.Application.Services.DrawApprovals.Models.Details;
using BlueTape.BackOffice.DecisionEngine.Application.Services.DrawApprovals.Models.List;
using BlueTape.BackOffice.DecisionEngine.Application.Services.Projects.Models;
using BlueTape.BackOffice.DecisionEngine.Domain.Enums;
using BlueTape.BackOffice.DecisionEngine.Domain.Models;
using BlueTape.CompanyService.BankAccounts;
using BlueTape.CompanyService.BankAccounts.Giact;
using BlueTape.CompanyService.Companies;
using BlueTape.CompanyService.Customers;
using BlueTape.Giact.API.ViewModels;
using BlueTape.Integrations.Giact.DTOs;
using BlueTape.InvoiceService.Models.Project.DetailedProject;
using BlueTape.LS.DTOs.Credit;
using BlueTape.LS.DTOs.Credit.CreditDetails;
using BlueTape.OBS.DTOs.CreditApplication;
using BlueTape.OBS.DTOs.CreditApplicationNotes;
using BlueTape.OBS.DTOs.DrawApprovalNotes;
using BlueTape.OBS.Enums;
using BlueTape.Utilities.Extensions;
using FirebaseAdmin.Auth;
using AddressModel = BlueTape.BackOffice.DecisionEngine.Application.Services.Admin.Models.AddressModel;
using BankAccountGiactModel = BlueTape.BackOffice.DecisionEngine.Application.Services.CreditApplications.Models.BankAccountGiactModel;
using CreditDetailsResponse = BlueTape.BackOffice.DecisionEngine.Api.Models.Responses.Project.CreditDetailsResponse;

namespace BlueTape.BackOffice.DecisionEngine.Api.Mappings;

public class ApiProfile : Profile
{
    public ApiProfile()
    {
        CreateMap(typeof(PaginatedList<>), typeof(PaginatedList<>)); // workaround to map paged lists
        CreateMap<AppConfigurationModel, AppConfigurationResponse>();
        CreateMap<LocConfigurationModel, LocConfigurationResponse>();
        CreateMap<GetPaidConfigurationModel, GetPaidConfigurationResponse>();
        CreateMap<ArAdvanceConfigurationModel, ArAdvanceConfigurationResponse>();
        CreateMap<DrawApprovalsConfigurationModel, DrawApprovalsConfigurationResponse>();
        CreateMap<RejectionReasons, RejectionReasonsResponse>();
        CreateMap<CreditApplicationsListQuery, CreditApplicationsListFilter>();
        CreateMap<CreditApplicationCompaniesQuery, CreditApplicationCompaniesFilter>();
        CreateMap<CreditApplicationsOwnerListQuery, CreditApplicationsOwnerListFilter>();
        CreateMap<ProjectsFilter, ProjectsFilterModel>();
        CreateMap<DrawApprovalsListQuery, DrawApprovalListFilter>().ForMember(x => x.AutomatedDecisionResult, opt => opt.MapFrom(y => y.AutomatedDecision));
        CreateMap<ApprovedAccountsListQuery, ApprovedAccountsFilter>().ForMember(x => x.ProductType,
            opt => opt.MapFrom(y =>
                y.Products != null ? y.Products.Select(type => type.UITypeToProductType()) : null));
        CreateMap<CreditApplicationDetailsModel, CreditApplicationDetailsResponse>();
        CreateMap<CreditApplicationDetailsModel, CreditApplicationDetailsDto>().ReverseMap();
        CreateMap<CreditApplicationRecord, CreditApplicationListResponse>()
            .ForMember(x => x.Status, opt => opt.MapFrom(y => y.UIStatus));

        CreateMap<ResultsDescription, ResultsDescriptionResponse>();

        CreateMap<Kyb, KybResponse>();
        CreateMap<Kyc, KycResponse>();
        CreateMap<Owner, OwnerResponse>();

        CreateMap(typeof(CheckResult<>), typeof(CheckResultResponse<>)).ReverseMap();

        CreateMap(typeof(DeStepResultDetails<>), typeof(DeStepResultDetailsResponse<>)).ReverseMap();
        CreateMap(typeof(CheckingRevenueResultDetails<>), typeof(CheckingRevenueResultDetailsResponse<>)).ReverseMap();
        CreateMap(typeof(CheckResultDetails<>), typeof(CheckResultDetailsResponse<>)).ReverseMap();
        CreateMap<ManualResultDetails, ManualResultDetailsResponse>().ReverseMap();
        CreateMap<LastExecutionDetails, LastExecutionDetailsResponse>().ReverseMap();

        CreateMap<ManualRefreshRunRequest, ManualRefreshRunModel>()
            .ForMember(x => x.ExecutionFlow,
                opt => opt.Ignore());
        CreateMap(typeof(CheckingRevenueResult<>), typeof(CheckingRevenueResultResponse<>));
        CreateMap<VerificationScore, VerificationScoreResponse>();
        CreateMap<PreliminaryChecks, PreliminaryChecksResponse>();
        CreateMap<CreditRating, CreditRatingResponse>();
        CreateMap<CreditApplicationDetails, CreditApplicationResponse>()
            .ForMember(x => x.Status, opt => opt.MapFrom(y => y.UIStatus));
        CreateMap<BankAccountsModel, BankAccountsResponse>();
        CreateMap<BankAccountDetails, BankAccountDetailsResponse>();
        CreateMap<BankAccountGiactModel, BankAccountGiactResponse>().ReverseMap();
        CreateMap<BankCreditStatus, BankCreditStatusResponse>();
        CreateMap<PatchBankAccountCashFlow, PatchBankAccountCashFlowModel>();
        CreateMap<CreateUpdateGuestSupplierRequest, CreateUpdateGuestSupplierModel>();
        CreateMap<UpdateGuestSupplierRequest, CreateUpdateGuestSupplierModel>();
        CreateMap<UpdateBankAccountNumberRequest, UpdateBankAccountNumberRequestModel>();
        CreateMap<NoSupplierBankDetailsRequest, NoSupplierBankDetailsRequestModel>();
        CreateMap<GiactVerificationResultRequest, GiactResponseModel>();
        CreateMap<GiactAccountVerificationReplyRequest, GiactVerificationModel>();
        CreateMap<GiactAccountAuthenticationReplyRequest, GiactAuthenticationModel>();
        CreateMap<AddressRequest, AddressModel>();
        CreateMap<CashFlowItem, CashFlowReportItemResponse>().ReverseMap();
        CreateMap<CreditApplicationNote, CreditApplicationNoteResponse>();
        CreateMap<NewCreditApplicationNoteRequest, NewCreditApplicationNote>();
        CreateMap<PatchCreditApplicationNoteDto, PatchCreditApplicationNote>()
            .ForMember(x => x.CreditApplicationId, opt => opt.Ignore())
            .ForMember(x => x.UpdatedBy, opt => opt.Ignore())
            .ForMember(x => x.Id, opt => opt.Ignore())
            .ReverseMap();
        CreateMap<FactoringPreliminaryChecksResponse, FactoringPreliminaryChecksModel>().ReverseMap();

        CreateMap<DrawApprovalType, DrawApprovalMethod>()
        .ConvertUsing(src =>
        src == DrawApprovalType.NoSupplier ? DrawApprovalMethod.DirectTerm :
        src == DrawApprovalType.Regular ? DrawApprovalMethod.Regular :
        src == DrawApprovalType.VirtualCard ? DrawApprovalMethod.VirtualCard :
        src == DrawApprovalType.Express ? DrawApprovalMethod.Express :
        src == DrawApprovalType.Custom ? DrawApprovalMethod.Custom :
        src == DrawApprovalType.Quote ? DrawApprovalMethod.Quote :
        src == DrawApprovalType.Factoring ? DrawApprovalMethod.Factoring :
        DrawApprovalMethod.None);

        CreateMap<DrawApprovalRecord, DrawApprovalResponse>()
            .ForMember(x => x.AutomatedDecision, opt => opt.MapFrom(y => y.AutomatedDecisionResult))
            .ForMember(x => x.Status, opt => opt.MapFrom(y => y.UIStatus));
        CreateMap<ProjectDetails, ProjectDetailsResponse>();
        CreateMap<PaymentPlanDetails, PaymentPlanDetailsResponse>();
        CreateMap<AccountDetails, AccountDetailsResponse>();
        CreateMap<PayablesDetails, PayablesDetailsResponse>();
        CreateMap<DrawAccountDetails, DrawAccountDetailsResponse>();
        CreateMap<FactoringRecordDetails, PaginatedFactoringDetailsResponse>()
            .ForMember(x => x.AccountStatus, opt => opt.MapFrom(s => s.Status));
        CreateMap<AccountRecord, AccountResponse>()
            .ForMember(x => x.Category, y => y.MapFrom(opt => opt.Category.ParseToEnum<BusinessCategoryType>()))
            .ForMember(x => x.Id, y => y.MapFrom(z => z.CompanyId));
        CreateMap<DrawApprovalCreditDetails, DrawApprovalCreditDetailsResponse>();
        CreateMap<DownPaymentDetails, DownPaymentDetailsResponse>();
        CreateMap<DrawApprovalDetailsRecord, DrawApprovalDetailsRecordResponse>()
            .ForMember(x => x.LoanOrigin, opt => opt.MapFrom(y => y.LoanOrigin))
            .ForMember(x => x.Status, opt => opt.MapFrom(y => y.UIStatus))
            .ForMember(x => x.DrawDetails, opt => opt.MapFrom(x => x.AccountDetails));
        CreateMap<DrawDetails, DrawDetailsResponse>();
        CreateMap<DrawPayablesDetails, DrawPayablesDetailsResponse>();
        CreateMap<DrawPaymentPlanDetails, DrawPaymentPlanDetailsResponse>();
        CreateMap<DrawProjectDetails, DrawProjectDetailsResponse>();
        CreateMap<DrawApprovalRecordDetails, DrawApprovalRecordDetailsResponse>();
        CreateMap<FactoringDetails, FactoringDetailsResponse>();
        CreateMap<FactoringOverallDetails, FactoringOverallDetailsResponse>();
        CreateMap<AutomatedApprovalDetails, AutomatedApprovalDetailsResponse>();
        CreateMap<NoSupplierDetails, NoSupplierDetailsResponse>();
        CreateMap<NoSupplierAddress, NoSupplierAddressResponse>().ReverseMap();
        CreateMap<NoSupplierBankDetails, NoSupplierBankDetailsResponse>().ReverseMap();
        CreateMap<NoSupplierBankAccountNumber, NoSupplierBankAccountNumberResponse>().ReverseMap();
        CreateMap<PayNowDetails, PayNowDetailsResponse>();
        CreateMap<IhcInternalChecks, IhcInternalChecksResponse>();
        CreateMap<IhcPreliminaryChecks, IhcPreliminaryChecksResponse>();
        CreateMap<PayablesSupplierDetails, PayablesSupplierDetailsResponse>();
        CreateMap<PreliminaryChecksDetails, PreliminaryChecksDetailsResponse>();
        CreateMap<ProjectCreditDetails, ProjectCreditDetailsResponse>();
        CreateMap<ProjectDetailsRecord, ProjectDetailsRecordResponse>();
        CreateMap<ReviewDrawApprovalRequest, ReviewDrawApproval>();
        CreateMap<ProjectListModel, ProjectListResponse>().ReverseMap();
        CreateMap<PaginatedList<ProjectListModel>, PaginatedList<ProjectListResponse>>().ReverseMap();
        CreateMap<CreditDetailsModel, CreditDetailsResponse>().ReverseMap();
        CreateMap<AccountIhcOverAllDetailsModel, AccountIhcOverAllDetailsResponse>();
        CreateMap<AccountCreditDetailsModel, AccountCreditDetailsResponse>()
            .ForMember(x => x.Type, opt => opt.MapFrom(s => s.PurchaseType));
        CreateMap<CashFlowListQuery, CashFlowListQueryModel>();
        CreateMap<GiactVerificationRequest, GAuthenicateRequestDTO>()
            .ForMember(dest => dest.BankAccountId, opt => opt.Ignore())
            .ForMember(dest => dest.LastName, opt => opt.Ignore())
            .ForMember(dest => dest.FirstName, opt => opt.Ignore());

        CreateMap<CreateManualBankAccountRequest, CreateManualBankAccountModel>();
        CreateMap<CreateManualBankAccountDetailsRequest, CreateManualBankAccountDetailsModel>();

        CreateMap<GAuthenicateResultDTO, GiactVerificationResponse>().ReverseMap();
        CreateMap<AccountAuthenticationReplyDTO, GiactAccountAuthenticationReply>().ReverseMap();
        CreateMap<AccountVerificationReplyDTO, GiactAccountVerificationReply>().ReverseMap();

        CreateMap<PatchDrawApprovalNoteDto, PatchDrawApprovalNote>()
            .ForMember(x => x.Id, opt => opt.Ignore())
            .ForMember(x => x.DrawApprovalId, opt => opt.Ignore());
        CreateMap<DrawApprovalNote, DrawApprovalNoteResponse>().ReverseMap();
        CreateMap<CreateDrawApprovalNoteDto, CreateDrawApprovalNote>()
            .ForMember(x => x.DrawApprovalId, opt => opt.Ignore());
        CreateMap<AttachSupplierRequest, AttachSupplierToDrawApproval>();
        CreateMap<SupplierInvitationDetailsRequest, SupplierInvitationDetails>();
        CreateMap<MasterAccountDetails, MasterAccountDetailsResponse>();
        CreateMap<IUserInfo, FireBaseUserResponse>();

        CreateMap<CustomerSettingsModel, CustomerSettingsResponse>()
            .ForMember(dest => dest.InHouseCredit, opt => opt.MapFrom(src => src.InHouseCredit));
        CreateMap<CustomerInHouseCreditModel, InHouseCreditResponse>();
        CreateMap<CompanyModel, CompanyOfCreditApplicationResponse>();

        MapDocumentVerificationModels();
        MapCreditModels();
        MapDeExecution();
    }

    private void MapCreditModels()
    {
        CreateMap<CreditDto, CreditResponse>();
        CreateMap<CreditDetailsDto, CreditCalculationDetailsResponse>();
    }

    private void MapDeExecution()
    {
        CreateMap<DeExecutionBankDetails, DeExecutionBankDetailsResponse>();
        CreateMap<DeExecutionCreditRating, DeExecutionCreditRatingResponse>();
        CreateMap<DeExecutionKyb, DeExecutionKybResponse>();
        CreateMap<DeExecutionKyc, DeExecutionKycResponse>();
        CreateMap<DeExecutionPreliminary, DeExecutionPreliminaryResponse>();
        CreateMap<DeLastExecutionsDetailed, DeLastExecutionsDetailedResponse>();
        CreateMap<DeCashFlowDetails, DeCashFlowDetailsResponse>();
    }

    private void MapDocumentVerificationModels()
    {
        CreateMap<DocumentVerification, DocumentVerificationResponse>()
            .ForMember(response => response.UploadedDocuments,
                y => y.MapFrom(model => model.ManuallyUploadedDocuments));

        CreateMap<UploadedDocument, UploadedDocumentResponse>()
            .ForMember(response => response.Url,
                y => y.MapFrom(model => model.DocumentUrl))
            .ForMember(response => response.Name,
                y => y.MapFrom(model => model.DocumentName))
            .ForMember(response => response.Date,
                y => y.MapFrom(model => model.CreatedAt))
            .ForMember(response => response.User,
                y => y.MapFrom(model => model.UserName))
            .ForMember(response => response.Note,
                y => y.MapFrom(model => model.Note))
            .ForMember(response => response.Id,
                y => y.MapFrom(model => model.Id.ToString()));

        CreateMap<DocumentApproval, AgreementResponseBase>()
            .ForMember(response => response.Signer,
                y => y.MapFrom(model => model.SignerName))
            .ForMember(response => response.Signature,
                y => y.MapFrom(model => model.IsApproved))
            .ForMember(response => response.Url,
                y => y.MapFrom(model => model.DocumentUrl))
            .ForMember(response => response.OwnerPercent,
                y => y.MapFrom(model => model.PercentOwned))
            .ForMember(response => response.Id,
                y => y.MapFrom(model => model.Id.ToString()))
            .ForMember(response => response.Date,
                y => y.MapFrom(model => model.ApprovalDate))
            .ForMember(response => response.FileName,
                y => y.MapFrom(model => model.DocumentName));

        CreateMap<DocumentApproval, PersonalGuarantorAgreementResponse>()
            .IncludeBase<DocumentApproval, AgreementResponseBase>();

        CreateMap<DocumentApproval, EConsentAgreementResponse>()
            .IncludeBase<DocumentApproval, AgreementResponseBase>();

        CreateMap<BankStatementDocument, BankStatementResponse>()
            .ForMember(response => response.Url,
                y => y.MapFrom(model => model.DocumentUrl))
            .ForMember(response => response.Id,
                y => y.MapFrom(model => model.Id.ToString()));

        CreateMap<AccountDetailedModel, AccountDetailedResponse>()
            .ForMember(response => response.EConsentLoCAgreement,
                y => y.MapFrom(model => model.DocumentApprovals))
            .ForMember(response => response.BankStatements,
                y => y.MapFrom(model => model.BankStatements))
            .ForMember(response => response.UploadedDocuments,
                y => y.MapFrom(model => model.ManuallyUploadedDocuments));

        CreateMap<NoteModel, OBS.DTOs.DrawApproval.Requests.NoteDto>().ReverseMap();
        CreateMap<OBS.DTOs.DrawApproval.Requests.PatchExpirationDateRequest, PatchExpirationDateModel>();
    }
}
