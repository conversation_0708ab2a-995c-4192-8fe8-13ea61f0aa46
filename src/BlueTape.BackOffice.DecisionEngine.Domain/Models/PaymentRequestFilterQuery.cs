using BlueTape.PaymentService.Enums;

namespace BlueTape.BackOffice.DecisionEngine.Domain.Models;

public class PaymentRequestFilterQuery
{
    public string? DrawId { get; set; }
    public string? Filter { get; set; }
    public List<string>? FlowTemplateCodes { get; set; }
    public PaymentRequestType? RequestType { get; set; }
    public List<string>? Status { get; set; }
    public DateOnly? ApprovedFrom { get; set; }
    public DateOnly? ApprovedTo { get; set; }
    public bool? IsConfirmed { get; set; }
    public string? SortOrder { get; set; }
    public string? SortBy { get; set; }
    public long? PageNumber { get; set; }
    public long? PageSize { get; set; }
}
