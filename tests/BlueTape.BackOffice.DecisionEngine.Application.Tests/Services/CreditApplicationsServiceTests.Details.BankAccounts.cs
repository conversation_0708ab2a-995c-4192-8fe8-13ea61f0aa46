using AutoMapper;
using BlueTape.BackOffice.DecisionEngine.Application.Constants.DecisionEngine;
using BlueTape.BackOffice.DecisionEngine.Application.Mappings;
using BlueTape.BackOffice.DecisionEngine.Application.Services.CreditApplications;
using BlueTape.BackOffice.DecisionEngine.DataAccess.External.CompanyApi;
using BlueTape.BackOffice.DecisionEngine.DataAccess.External.LoanApi.Proxy;
using BlueTape.BackOffice.DecisionEngine.DataAccess.External.OnboardingApi;
using BlueTape.BackOffice.DecisionEngine.Domain.Enums;
using BlueTape.CompanyService.BankAccounts;
using BlueTape.Firebase.Abstractions;
using BlueTape.OBS.DTOs.AccountAuthorization;
using BlueTape.OBS.DTOs.CreditApplication;
using BlueTape.OBS.DTOs.CreditApplicationAuthorizationDetails;
using BlueTape.OBS.DTOs.DecisionEngineSteps;
using BlueTape.Utilities.Providers;
using NSubstitute;
using Shouldly;
using System.Diagnostics.CodeAnalysis;

namespace BlueTape.BackOffice.DecisionEngine.Application.Tests.Services;

[SuppressMessage("ReSharper", "InconsistentNaming")]
public class CreditApplicationsServiceTests_Details_BankAccounts
{
    private const string AppId = "123";
    private const string CompanyId = "companyId";
    private const string ExecutionId = "ExecutionId";
    private readonly DateTime CurrentDateTime = DateTime.UtcNow;

    private readonly CreditApplicationsService _creditApplicationsService;
    private readonly IOnboardingApiProxy _onboardingApiProxy = Substitute.For<IOnboardingApiProxy>();
    private readonly ICompanyApiProxy _companyApiProxy = Substitute.For<ICompanyApiProxy>();
    private readonly ILoanApiProxy _loanApiProxy = Substitute.For<ILoanApiProxy>();
    private readonly IDateProvider _dateProvider = Substitute.For<IDateProvider>();
    private readonly IFirebaseClient _firebaseClient = Substitute.For<IFirebaseClient>();
    private readonly CancellationToken _ct = CancellationToken.None;

    public CreditApplicationsServiceTests_Details_BankAccounts()
    {
        var mapperConfig = new MapperConfiguration(c => c.AddProfile(new ApplicationProfile()));
        var mapper = mapperConfig.CreateMapper();

        _onboardingApiProxy
            .GetCreditApplication(AppId, Arg.Any<CancellationToken>())
            .Returns(new CreditApplicationDto
            {
                ExecutionId = ExecutionId,
                CompanyId = CompanyId,
                AutomatedDecisionResult = AutomatedDecisionType.Pass.ToString(),
                Status = CreditApplicationStatus.Processed.ToString()
            });

        _dateProvider.CurrentDateTime.Returns(CurrentDateTime);
        _creditApplicationsService = new CreditApplicationsService(_onboardingApiProxy, _companyApiProxy, _dateProvider, mapper, _firebaseClient, _loanApiProxy);
    }

    [Fact]
    public async Task GetCreditApplication_BankAccounts_ShouldFillMainResult()
    {

        var display = "********1234";
        var bankAccountName = "Bank1 / ";
        var secondBankAccountName = "Bank2 / some text";
        var thirdBankAccountName = "Bank3 / extra text";
        var expectedBankAccountName = $"Bank1 {display}";
        var expectedSecondBankAccountName = $"{secondBankAccountName} {display}";
        var steps = new List<DecisionEngineStepsDto>()
        {
            new()
            {
                Step = DecisionSteps.BankAccountVerification, Status = AutomatedDecisionType.Pass.ToString(),
                Results =
                [
                    new DecisionEngineStepResultDto
                    {
                        ComparisonSource = DecisionSources.BankAccounts.CheckingAccountExists,
                        BankAccountIdentifier = "1",
                        Result = "Pass",
                        ComparisonValue = "checking"
                    },
                    new DecisionEngineStepResultDto
                    {
                        ComparisonSource = DecisionSources.BankAccounts.BankAccountNameAndAddress,
                        BankAccountIdentifier = "1",
                        Result = "Pass"
                    },
                    new DecisionEngineStepResultDto
                    {
                        ComparisonSource = DecisionSources.BankAccounts.CheckingAccountExists,
                        BankAccountIdentifier = "2",
                        Result = "Pass",
                        ComparisonValue = "checking"
                    },
                    new DecisionEngineStepResultDto
                    {
                        ComparisonSource = DecisionSources.BankAccounts.BankAccountNameAndAddress,
                        BankAccountIdentifier = "2",
                        Result = "Pass"
                    }
                ]
            }
        };

        _onboardingApiProxy
            .GetDecisionEngineStepsByExecutionId(ExecutionId, Arg.Any<CancellationToken>())
            .Returns(steps);

        _onboardingApiProxy
            .GetApplicationAuthorizationDetails(AppId, Arg.Any<CancellationToken>())
            .Returns(new CreditApplicationAuthorizationDetailsDto
            {
                AccountAuthorizationDetailsSnapshot = new()
                {
                    BankAccountDetails = [
                        new BankAccountDetailsDto { Id = "1", Name = "Bank1" },
                        new BankAccountDetailsDto { Id = "2", Name = "Bank2" }
                    ]
                }
            });

        _companyApiProxy
            .GetBankAccountsByCompanyId(CompanyId, _ct)
            .Returns(new List<BankAccountModel>
            {
                new() {Id = "1", AccountName = bankAccountName, IsPrimary = true,AccountNumber = new() {Display = display}, RoutingNumber = "number", AccountHolderName = "name", IsManualEntry = true, IsRegulated = true},
                new() {Id = "2", Name = secondBankAccountName, IsPrimary = false, AccountNumber = new(){Display = display}, Plaid = new BankAccountPlaidModel{IncludeInCashFlow = true, Status = "active"} , IsManualEntry = false, IsRegulated = false},
                new() {Id = "3", Name = thirdBankAccountName, IsPrimary = false, AccountNumber = new(){Display = display}, AccountHolderName = "name", Plaid = new BankAccountPlaidModel{IncludeInCashFlow = true, Status = "active"} , IsManualEntry = false}
            });

        var details = await _creditApplicationsService.GetCreditApplicationDetails(AppId, _ct);
        details.ShouldNotBeNull();
        details.BankDetails.ShouldNotBeNull();
        details.BankDetails.Result.ShouldNotBeNull();
        details.BankDetails.BankAccounts.Count.ShouldBe(3);

        var maualAccount = details.BankDetails.BankAccounts[0];
        maualAccount.Name.ShouldBe(expectedBankAccountName);
        maualAccount.Identifier.ShouldBe("1");
        maualAccount.IsCheckingAccount!.Result.ShouldBe(null);
        maualAccount.IsCheckingAccount!.Value.ShouldBe(false);
        maualAccount.BusinessNameVerification.ShouldBe(null);
        maualAccount.PlaidConnectionStatus.ShouldBe(null);
        maualAccount.ConnectionType.ShouldBe(BankAccountConnectionType.Manual);
        maualAccount.IsPrimary.ShouldBe(true);

        var plaidAccount = details.BankDetails.BankAccounts[1];
        plaidAccount.Name.ShouldBe(expectedSecondBankAccountName);
        plaidAccount.Identifier.ShouldBe("2");
        plaidAccount.IsCheckingAccount!.Result.ShouldBe(AutomatedDecisionType.Pass);
        plaidAccount.IsCheckingAccount!.Value.ShouldBe(true);
        plaidAccount.BusinessNameVerification.ShouldBe(AutomatedDecisionType.Pass);
        plaidAccount.PlaidConnectionStatus.ShouldBe(PlaidConnectionStatus.Active);
        plaidAccount.ConnectionType.ShouldBe(BankAccountConnectionType.Plaid);
        plaidAccount.IsPrimary.ShouldBe(false);

        var plaidAccount2 = details.BankDetails.BankAccounts[2];
        plaidAccount2.Name.ShouldBe($"{thirdBankAccountName} {display}");
        plaidAccount2.Identifier.ShouldBe("3");
        plaidAccount2.IsCheckingAccount!.Result.ShouldBe(null);
        plaidAccount2.IsCheckingAccount!.Value.ShouldBe(false);
        plaidAccount2.BusinessNameVerification.ShouldBe(AutomatedDecisionType.Pass);
        plaidAccount2.PlaidConnectionStatus.ShouldBe(PlaidConnectionStatus.Active);
        plaidAccount2.ConnectionType.ShouldBe(BankAccountConnectionType.Plaid);
        plaidAccount2.IsPrimary.ShouldBe(false);
        plaidAccount2.AccountHolderName.ShouldBe("name");
        plaidAccount2.AccountNumber.ShouldBe(display);

        var giact = details.BankDetails.Giact[0];
        giact.RoutingNumber.ShouldBe("number");
        giact.AccountHolderName.ShouldBe("name");
        giact.AccountNumber.ShouldBe(display);
        giact.Name.ShouldBe(expectedBankAccountName);
    }
}
