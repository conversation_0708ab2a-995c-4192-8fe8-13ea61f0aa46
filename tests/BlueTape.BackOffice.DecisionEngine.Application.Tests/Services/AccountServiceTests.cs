using System.Collections.ObjectModel;
using AutoFixture;
using AutoMapper;
using BlueTape.BackOffice.DecisionEngine.Application.Constants;
using BlueTape.BackOffice.DecisionEngine.Application.Constants.DecisionEngine;
using BlueTape.BackOffice.DecisionEngine.Application.Services.Accounts.Constants;
using BlueTape.BackOffice.DecisionEngine.Application.Services.Accounts.Generators;
using BlueTape.BackOffice.DecisionEngine.Application.Services.Accounts.Models;
using BlueTape.BackOffice.DecisionEngine.Application.Services.Accounts.Models.List;
using BlueTape.BackOffice.DecisionEngine.Application.Services.Companies.Models;
using BlueTape.BackOffice.DecisionEngine.Application.Services.CreditApplications.Models;
using BlueTape.BackOffice.DecisionEngine.Application.Services.CreditApplications.Models.DocumentVerification;
using BlueTape.BackOffice.DecisionEngine.Application.Services.Reports;
using BlueTape.BackOffice.DecisionEngine.DataAccess.External.CompanyApi;
using BlueTape.BackOffice.DecisionEngine.DataAccess.External.LoanApi.Proxy;
using BlueTape.BackOffice.DecisionEngine.DataAccess.External.OnboardingApi;
using BlueTape.BackOffice.DecisionEngine.DataAccess.External.PlaidApi.Proxy;
using BlueTape.BackOffice.DecisionEngine.DataAccess.External.Tests.Attributes;
using BlueTape.BackOffice.DecisionEngine.Domain.Enums;
using BlueTape.BackOffice.DecisionEngine.Domain.Enums.UI;
using BlueTape.BackOffice.DecisionEngine.Domain.Models;
using BlueTape.Common.ExceptionHandling.Exceptions;
using BlueTape.CompanyService.BankAccounts;
using BlueTape.CompanyService.CashFlow.Queries;
using BlueTape.CompanyService.CashFlow.Responses;
using BlueTape.CompanyService.Common.Enums;
using BlueTape.CompanyService.Companies;
using BlueTape.CompanyService.Customers;
using BlueTape.CompanyService.Documents.Responses;
using BlueTape.Integrations.Plaid.Assets.Responses;
using BlueTape.LS.Domain.Enums;
using BlueTape.LS.DTOs.Credit;
using BlueTape.LS.DTOs.Credit.CreditDetails;
using BlueTape.LS.DTOs.Loan;
using BlueTape.OBS.DTOs.AccountAuthorization;
using BlueTape.OBS.DTOs.CreditApplication;
using BlueTape.OBS.DTOs.CreditApplication.Queries;
using BlueTape.OBS.DTOs.CreditApplicationAuthorizationDetails;
using BlueTape.OBS.DTOs.DecisionEngineSteps;
using BlueTape.OBS.Enums;
using BlueTape.Utilities.Providers;
using Microsoft.AspNetCore.Mvc;
using NSubstitute;
using Shouldly;
using CreditApplicationStatus = BlueTape.BackOffice.DecisionEngine.Domain.Enums.CreditApplicationStatus;
using CreditStatus = BlueTape.LS.Domain.Enums.CreditStatus;
using DebtInvestorType = BlueTape.CompanyService.Common.Enums.DebtInvestorType;
using PlaidConnectionStatus = BlueTape.BackOffice.DecisionEngine.Domain.Enums.PlaidConnectionStatus;
using PurchaseTypeOption = BlueTape.LS.Domain.Enums.PurchaseTypeOption;
using TemplateType = BlueTape.CompanyService.Common.Enums.TemplateType;
using CompanyIdsDto = BlueTape.OBS.DTOs.CreditApplication.Queries.CompanyIdsDto;

namespace BlueTape.BackOffice.DecisionEngine.Application.Tests.Services;

public class AccountServiceTests
{
    private const string CompanyId = "companyId";
    private readonly DateOnly _currentDateOnly = DateOnly.FromDateTime(DateTime.UtcNow);
    private readonly DateTime _currentDateTime = DateTime.UtcNow;

    private readonly ICompanyApiProxy _companyApi = Substitute.For<ICompanyApiProxy>();
    private readonly ILoanApiProxy _loanApi = Substitute.For<ILoanApiProxy>();
    private readonly IOnboardingApiProxy _onboardingApi = Substitute.For<IOnboardingApiProxy>();
    private readonly IMapper _mapper = Substitute.For<IMapper>();
    private readonly IPlaidApiProxy _plaidApiProxy = Substitute.For<IPlaidApiProxy>();
    private readonly IReportFileGenerator _fileGenerator = Substitute.For<IReportFileGenerator>();
    private readonly IDateProvider _dateProvider = Substitute.For<IDateProvider>();
    private readonly Application.Services.Accounts.AccountService _accountService;
    private readonly Fixture _fixture = new();

    public AccountServiceTests()
    {
        _dateProvider.CurrentDateTime.Returns(_currentDateTime);
        _dateProvider.CurrentDate.Returns(_currentDateOnly);
        _accountService = new Application.Services.Accounts.AccountService(
            _mapper, _companyApi, _loanApi, _onboardingApi,
            _plaidApiProxy, _fileGenerator, new AccountCreditDetailsGenerator(_dateProvider));
    }

    [Fact]
    public async Task GetCompanyCashFlowFile_ReturnsFileStreamResult()
    {
        var companyId = "testCompanyId";
        var date = DateOnly.FromDateTime(DateTime.UtcNow.Date);
        var cashFlowItems = new List<CashFlowItemResponse>
        {
            new() { AccountId = "accountId1", Date = date, Debit = 100, Credit = 50, Balance = 50, AssetReportId = "reportId1", CashFlowResult = 250 },
            new() { AccountId = "accountId2", Date = date, Debit = 200, Credit = 100, Balance = 100, AssetReportId = "reportId2", CashFlowResult = 380 }
        };
        var cashFlow = new CashFlowResponse { CashFlowItems = cashFlowItems };
        var bankAccounts = new List<BankAccountModel>
        {
            new() { Id = "accountId1", Name = "Bank1", AccountNumber = new() { Display = "****1234" } },
            new() { Id = "accountId2", Name = "Bank2", AccountNumber = new() { Display = "****5678" } }
        };
        var company = new CompanyModel { Id = companyId, Name = "Test Company" };

        _fileGenerator.GenerateReportTimeStamp().Returns("timestamp");
        _companyApi.GetCashFlow(companyId, Arg.Any<AssetReportQuery>(), default).Returns(cashFlow);
        _companyApi.GetBankAccountsByAccountAndPlaidIds(Arg.Any<string[]>(), default).Returns(bankAccounts);
        _companyApi.GetCompanyById(companyId, default).Returns(company);
        _fileGenerator.GenerateReportFileStreamResult(Arg.Is<IList<CashFlowRecord>>(records =>
                    records.Count == cashFlowItems.Count &&
                    records.First().AccountId == cashFlowItems.First().AccountId &&
                    records.First().Balance == cashFlowItems.First().Balance &&
                    records.First().AccountDisplay == bankAccounts.First().AccountNumber!.Display &&
                    records.ElementAt(1).AccountId == cashFlowItems.ElementAt(1).AccountId &&
                    records.ElementAt(1).Balance == cashFlowItems.ElementAt(1).Balance &&
                    records.ElementAt(1).AccountDisplay == bankAccounts.ElementAt(1).AccountNumber!.Display),
                ReportType.Xlsx, $"{company.Name}_{ReportsConstants.CashFlowReportName}_timestamp")
            .Returns(new FileStreamResult(new MemoryStream(), ReportsConstants.ReportContentType)
            {
                FileDownloadName = "name.xlsx"
            });

        var result = await _accountService.GetCompanyCashFlowFile(companyId, default);

        result.ShouldNotBeNull();
        result.ContentType.ShouldBe(ReportsConstants.ReportContentType);
        result.FileDownloadName.ShouldBe("name.xlsx");
        Assert.IsType<FileStreamResult>(result);
        await _companyApi.Received(1).GetCashFlow(companyId, Arg.Any<AssetReportQuery>(), default);
        await _companyApi.Received(1).GetBankAccountsByAccountAndPlaidIds(Arg.Any<string[]>(), default);
        await _companyApi.Received(1).GetCompanyById(companyId, default);
        await _fileGenerator.Received(1).GenerateReportFileStreamResult(Arg.Any<List<CashFlowRecord>>(), ReportType.Xlsx, Arg.Any<string>());
    }

    [Fact]
    public Task GetCompanyCashFlowTransactionsFile_NoAssetReportRequestFound_ThrowsVariableNullException()
    {
        _plaidApiProxy.GetAssetRequestsByCompanyId(CompanyId, 1, true, Arg.Any<CancellationToken>())
            .Returns((List<PlaidAssetReportRequestModelViewModel>?)null);

        return Should.ThrowAsync<VariableNullException>(async () =>
            await _accountService.GetCompanyCashFlowTransactionsFile(CompanyId, CancellationToken.None));
    }

    [Fact]
    public async Task GetCompanyCashFlowTransactionsFile_AssetReportIsNullOrEmpty_ReturnsEmptyCsv()
    {
        var date = new DateTime(2020, 05, 06);
        var timestamp = date.ToString(ReportsConstants.TimeStampFormat);
        var assetReportRequest = _fixture.Create<PlaidAssetReportRequestModelViewModel>();
        _plaidApiProxy.GetAssetRequestsByCompanyId(CompanyId, 1, true, Arg.Any<CancellationToken>())
            .Returns(new List<PlaidAssetReportRequestModelViewModel>() { assetReportRequest });
        _plaidApiProxy.GetAssetReportByToken(assetReportRequest.AssetReportToken, Arg.Any<CancellationToken>())
            .Returns((GetAssetReportResponseModel?)null);
        var company = _fixture.Create<CompanyModel>();
        _companyApi.GetCompanyById(CompanyId, Arg.Any<CancellationToken>()).Returns(company);
        _fileGenerator.GenerateReportTimeStamp().Returns(timestamp);
        _fileGenerator.GenerateReportFileStreamResult(
            Arg.Is<IList<CashFlowTransactionRecord>>(x => x.Count == 0),
            ReportType.Xlsx, $"{company.Name}_{ReportsConstants.CashFlowTransactionsReportName}_{timestamp}").Returns(
            new FileStreamResult(new MemoryStream(), ReportsConstants.ReportContentType)
            {
                FileDownloadName = $"{company.Name}_{ReportsConstants.CashFlowTransactionsReportName}_{timestamp}.xlsx"
            });

        var result = await _accountService.GetCompanyCashFlowTransactionsFile(CompanyId, default);

        result.ShouldNotBeNull();
        result.FileDownloadName.ShouldStartWith(company.Name);
        result.FileDownloadName.ShouldContain(timestamp);
        result.FileDownloadName.ShouldContain("Transactions");
        result.FileDownloadName.ShouldEndWith(".xlsx");
    }

    [Fact]
    public async Task GetCompanyCashFlowTransactionsFile_ValidAssetReport_ReturnsCsvWithTransactions()
    {
        var assetReportRequest = _fixture.Create<PlaidAssetReportRequestModelViewModel>();
        var assetReport = _fixture.Create<AssetReportModel>();
        var assetReportResponse = new GetAssetReportResponseModel { AssetReport = assetReport };
        _plaidApiProxy.GetAssetRequestsByCompanyId(CompanyId, 1, true, Arg.Any<CancellationToken>())
            .Returns(new List<PlaidAssetReportRequestModelViewModel>() { assetReportRequest });
        _plaidApiProxy.GetAssetReportByToken(assetReportRequest.AssetReportToken, Arg.Any<CancellationToken>())
            .Returns(assetReportResponse);
        var company = _fixture.Create<CompanyModel>();
        _companyApi.GetCompanyById(CompanyId, Arg.Any<CancellationToken>()).Returns(company);
        _fileGenerator.GenerateReportTimeStamp().Returns("timestamp");
        _fileGenerator.GenerateReportFileStreamResult(Arg.Any<IList<CashFlowTransactionRecord>>(), ReportType.Xlsx, Arg.Any<string>())
            .Returns(new FileStreamResult(new MemoryStream(), ReportsConstants.ReportContentType)
            {
                FileDownloadName = $"{company.Name}_{ReportsConstants.CashFlowTransactionsReportName}_timestamp.xlsx"
            });

        var result = await _accountService.GetCompanyCashFlowTransactionsFile(CompanyId, default);

        result.ShouldNotBeNull();
        await _plaidApiProxy.Received(1).GetAssetRequestsByCompanyId(CompanyId, 1, true, Arg.Any<CancellationToken>());
        await _plaidApiProxy.Received(1).GetAssetReportByToken(assetReportRequest.AssetReportToken, Arg.Any<CancellationToken>());
        await _companyApi.Received(1).GetCompanyById(CompanyId, Arg.Any<CancellationToken>());
    }

    [Fact]
    public async Task GetApprovedAccounts_FilterWithNoCompanies_ReturnsEmptyPaginatedList()
    {
        var filter = _fixture.Build<ApprovedAccountsFilter>()
            .Without(f => f.AccountStatus)
            .Create();
        var query = new CompanyQueryPaginated() { };

        _mapper.Map<CompanyQueryPaginated>(filter).Returns(query);
        _companyApi.GetCompaniesList(Arg.Is<CompanyQueryPaginated>(x => x.PageSize == int.MaxValue && x.PageNumber == 1), Arg.Any<CancellationToken>()).Returns(new PaginatedCompanyResponse() { Result = new List<CompanyModel>() });
        _onboardingApi.GetCreditApplicationsList(Arg.Is<CreditApplicationQueryDto>(x => x.Status!.Contains(CreditApplicationStatus.Approved.ToString())), default).Returns(new List<CreditApplicationDto>());
        var result = await _accountService.GetApprovedAccounts(filter, CancellationToken.None);

        result.ShouldNotBeNull();
        result.Result.ShouldBeEmpty();
        result.TotalCount.ShouldBe(0);
    }

    [Fact]
    public async Task GetApprovedAccounts_CompaniesFoundWithMatchingFilters_ReturnsCorrectAccounts()
    {
        _fixture.Register(() => DateOnly.FromDateTime(DateTime.Today));

        var filter = _fixture.Build<ApprovedAccountsFilter>()
            .With(f => f.PageSize, 2)
            .With(f => f.PageNumber, 1)
            .Create();
        var query = new CompanyQueryPaginated();
        var companies = new PaginatedCompanyResponse
        {
            Result = _fixture.CreateMany<CompanyModel>(2).ToList(),
            Total = 2,
            Offset = 1,
            Count = 1
        };

        var creditApplications = _fixture.Build<CreditApplicationDto>()
            .With(x => x.Status, nameof(OBS.Enums.CreditApplicationStatus.Approved))
            .CreateMany(2).ToList();

        // Explicitly link credit applications to companies
        creditApplications[0].CompanyId = companies.Result[0].Id;
        creditApplications[1].CompanyId = companies.Result[1].Id;

        var credits = new List<CreditDto?>
        {
            _fixture.Build<CreditDto>()
                .With(x => x.CompanyId, companies.Result[0].Id)
                .With(x => x.Product, ProductType.LineOfCredit)
                .Create(),
            _fixture.Build<CreditDto>()
                .With(x => x.CompanyId, companies.Result[1].Id)
                .With(x => x.Product, ProductType.LineOfCredit)
                .Create()
        };

        _mapper.Map<CompanyQueryPaginated>(filter).Returns(query);
        _companyApi.GetCompaniesList(Arg.Is<CompanyQueryPaginated>(x => x.PageSize == int.MaxValue && x.PageNumber == 1),
            Arg.Any<CancellationToken>()).Returns(companies);
        _onboardingApi.GetCreditApplicationsByCompanyIds(
                Arg.Is<CompanyIdsDto>(x => x.Ids.SequenceEqual(companies.Result.Select(c => c.Id))),
                Arg.Any<GetCreditApplicationsByCompanyIdsQueryDto>(),
                Arg.Any<CancellationToken>())
            .Returns(creditApplications);
        _loanApi.GetCreditsByCompanyIdArray(Arg.Any<string[]>(), false, Arg.Any<CancellationToken>())
            .Returns(credits);

        var result = await _accountService.GetApprovedAccounts(filter, CancellationToken.None);

        result.ShouldNotBeNull();
        result.Result.Count().ShouldBe(2);
        result.TotalCount.ShouldBe(2);

        await _companyApi.Received(1).GetCompaniesList(query, Arg.Any<CancellationToken>());
        await _onboardingApi.Received(1).GetCreditApplicationsByCompanyIds(
            Arg.Any<CompanyIdsDto>(),
            Arg.Any<GetCreditApplicationsByCompanyIdsQueryDto>(),
            Arg.Any<CancellationToken>());
        await _loanApi.Received(1).GetCreditsByCompanyIdArray(Arg.Any<string[]>(), false, Arg.Any<CancellationToken>());
    }

    [Fact]
    public async Task GetApprovedAccounts_EmptyFilters_ShouldFallbackToDefaultStatuses()
    {
        var filter = new ApprovedAccountsFilter
        {
            AccountStatus = Array.Empty<AccountStatusEnum>(),
            PageNumber = 1,
            PageSize = 2
        };
        var query = new CompanyQueryPaginated();
        var companies = new PaginatedCompanyResponse
        {
            Result = _fixture.CreateMany<CompanyModel>(2).ToList(),
            Total = 2,
            Offset = 1,
            Count = 1
        };

        _mapper.Map<CompanyQueryPaginated>(filter).Returns(query);
        _companyApi.GetCompaniesList(query, Arg.Any<CancellationToken>()).Returns(companies);

        var result = await _accountService.GetApprovedAccounts(filter, CancellationToken.None);

        result.ShouldNotBeNull();
        result.TotalCount.ShouldBe(2);

        filter.AccountStatus.ShouldBe(AccountFilters.ApprovedAccountStatuses);
    }

    [Fact]
    public async Task GetAccountCashFlow_ValidRequest_ReturnsPaginagtedCashFlows()
    {
        _fixture.Customize<CashFlowItemResponse>(composer => composer
            .With(x => x.Date, _currentDateOnly));
        _fixture.Customize<CashFlowItem>(composer => composer.With(x => x.Date, _currentDateOnly));
        var expectedResult = _fixture.Create<PaginatedList<CashFlowItem>>();
        var response = _fixture.Create<PaginatedList<CashFlowItemResponse>>();
        var query = new CashFlowListQueryModel
        {
            PageNumber = 1,
            PageSize = 4
        };

        _companyApi.GetPaginatedCashFlow(CompanyId, query.PageNumber, query.PageSize, default).Returns(response);
        _mapper.Map<PaginatedList<CashFlowItem>>(response).Returns(expectedResult);

        var result = await _accountService.GetAccountCashFlows(CompanyId, query, default);

        result.ShouldBeEquivalentTo(expectedResult);
    }

    [Fact]
    public async Task GetDetailedAccount_ValidCompanyId_ShouldReturnAccountDetailedModel()
    {
        var creditId = Guid.NewGuid();
        var bankAccountName = "BankName / CreditCurdType";
        var display = "********1234";
        var expectedBankAccountName = $"{bankAccountName} {display}";
        var executionId = "executionId";

        var documentId = Guid.NewGuid();
        var bankAccountId = "bankAccountId";
        var merchantId = nameof(CreditDto.MerchantId);
        var productType = ProductType.LineOfCredit;
        var uiProductType = UICreditApplicationType.loc;
        var companyModel = new CompanyModel
        {
            Id = CompanyId,
            Name = "CompanyName",
            AccountStatus = AccountStatusEnum.Active,
            StatusEvent = "StatusEvent",
            LegalName = "LegalName"
        };
        var customerModel = new CustomerModel
        {
            BlueTapeCustomerId = Guid.NewGuid().ToString(),
            CompanyId = merchantId,
            Settings = new CustomerSettingsModel() { DebtInvestorTradeCredit = DebtInvestorType.Aion }
        };

        var targetCredit = new CreditDto()
        {
            Id = creditId,
            CreditLimit = 20000,
            Product = productType,
            MerchantId = merchantId,
            Status = CreditStatus.OnHold,
            RevenueFallPercentage = 80,
            PurchaseType = PurchaseTypeOption.Project,
            CreditDetails = new CreditDetailsDto()
            {
                DrawOutstandingAmount = 0,
                AvailableCredit = 10000,
                NumberOfDraws = 3,
                ProcessingAmount = 100,
                LateAmount = 55,
                NumberOfPastDueDraws = 1,
                TotalActiveDrawAmount = 1000,
                NumberOfActiveDraws = 2
            }
        };
        var credits = new List<CreditDto>()
        {
            targetCredit,
            new()
            {
                Status = CreditStatus.Active,
                Product = ProductType.InHouseCredit
            },
            new()
            {
                Status = CreditStatus.OnHold,
                Product = ProductType.InHouseCredit
            }
        };

        var accountAuth = new AccountAuthorizationDto()
        {
            EinHash = "EinHash",
            BusinessDetails = new()
            {
                AnnualRevenue = 1000,
                CompanyIncome = 10000,
                TotalAcceptableDebtAmount = 3000,
            },
            BankAccountDetails = new List<BankAccountDetailsDto>()
            {
                new()
                {
                    Id = bankAccountId
                }
            }
        };
        var targetCreditApp = new CreditApplicationDto()
        {
            Id = "creditAppId",
            ExecutionId = executionId,
            MerchantId = merchantId,
            Type = CreditApplicationType.LineOfCredit
        };

        var creditApps = new List<CreditApplicationDto>()
        {
            targetCreditApp,
            new()
            {
                Id = "creditAppId",
                ExecutionId = executionId,
                MerchantId = merchantId,
                Type = CreditApplicationType.InHouseCredit,
                Status = CreditApplicationStatus.Processed.ToString()
            }
        };

        var ownerId = "Owner";
        var authDetails = new CreditApplicationAuthorizationDetailsDto()
        {
            CreditApplicationId = "CreditApplicationId",
            AccountAuthorizationDetailsSnapshot = new AccountAuthorizationDto
            {
                OwnersDetails =
                [
                    new OwnersDetailsDto
                    {
                        Id = ownerId,
                        Identifier = ownerId,
                        PercentOwned = 2
                    }
                ],
            }
        };

        var documentLoanApplicationResponse = new List<DocumentLoanApplicationResponse>()
        {
            new()
            {
                Template = new TemplateResponse()
                {
                    TemplateType = TemplateType.MASTER_AGREEMENT
                },
                Id = documentId,
                DocumentApprovals = new List<DocumentApprovalResponse>()
                {
                    new()
                    {
                        Id = documentId,
                        OwnerIdentifier = ownerId,
                        FirstName = "FirstName",
                        LastName = "LastName",
                        DocumentId = documentId.ToString(),
                        IpAddress = "IpAddress",
                        IsApproved = true,
                        ApprovedAt = _currentDateTime,
                    },
                },
                S3Url = "source:/url/document.docx",
            }
        };

        var bankAccounts = new BankAccountModel()
        {
            Id = bankAccountId,
            IsManualEntry = false,
            AccountName = bankAccountName,
            Plaid = new BankAccountPlaidModel()
            {
                IncludeInCashFlow = true,
                Status = "Active"
            },
            AccountNumber = new AccountNumberModel
            {
                Display = display,
            },
            AccountHolderName = "name",
            RoutingNumber = "number"
        };

        var step = new DecisionEngineStepsDto
        {
            Step = DecisionSteps.BankAccountVerification,
            ExecutionId = executionId,
            Results = new DecisionEngineStepResultDto[]
            {
                new()
                {
                    BankAccountIdentifier = bankAccountId,
                    ComparisonSource = "Type",
                    ComparisonValue = "checking",
                    Result = "Pass"
                },
                new()
                {
                    BankAccountIdentifier = bankAccountId,
                    ComparisonSource = "BankAccountNameAndAddressVerificationRuleResult",
                    Result = "Pass"
                },
            }
        };

        var decisionEngineSteps = new List<DecisionEngineStepsDto>()
        {
            step,
            new()
            {
                CreatedAt = _currentDateTime,
                ExecutionType = ExecutionType.Scheduled.ToString()
            }
        };

        var loans = new List<LoanDto>
        {
            new()
            {
                CompanyId = null,
                EinHash = null,
                LoanDetails = new LoanDetailsDto
                {
                    IsFullyPaid = false,
                    TotalPaid = 200,
                    LateAmount = 300
                },
                LoanOrigin = LoanOrigin.Normal,
                Payments = null,
                CreditId = null,
                Status = LoanStatus.Started,
                MerchantId = null,
                FundingSource = FundingSource.Arcadia
            },
            new()
            {
                LoanDetails = new LoanDetailsDto()
                {
                    LoanOutstandingAmount = 100
                }
            },
            new()
            {
                LoanDetails = new LoanDetailsDto()
                {
                    NextPaymentDate = DateOnly.FromDateTime(_currentDateTime),
                    NextPaymentAmount = 400
                }
            },
            new()
            {
                LoanDetails = new()
                {
                    OldestDueOrPastDueDate = DateOnly.FromDateTime(_currentDateTime).AddDays(-2)
                }
            }
        };

        var bankStatements = new List<BankStatementResponse>();
        _loanApi.GetCreditsByFilters(Arg.Is<CreditFilterDto>(c => c.CompanyId == companyModel.Id && c.Detailed == true), default).Returns(credits!);
        _companyApi.GetBankAccountsByCompanyId(companyModel.Id, default).Returns([bankAccounts]);
        _companyApi.GetCompanyById(companyModel.Id, default).Returns(companyModel);
        _companyApi.GetCustomersByMerchantIdAndCompanyId(merchantId, companyModel.Id, default).Returns([customerModel]);
        _onboardingApi.GetAccountAuthorizationByCompanyId(companyModel.Id, default).Returns(accountAuth);
        _companyApi.GetDocuments(companyModel.Id, default).Returns(documentLoanApplicationResponse);
        _onboardingApi.GetCreditApplicationsList(Arg.Is<CreditApplicationQueryPaginatedDto>(x => x.CompanyId == companyModel.Id && x.MerchantId == merchantId && x.Type!.SequenceEqual(new[]
        {
            CreditApplicationType.LineOfCredit.ToString()
        })), default).Returns(creditApps);
        _loanApi.GetLoansByQuery(Arg.Any<LoanQueryDto>(), default).Returns(loans!);
        _onboardingApi.GetApplicationAuthorizationDetails(targetCreditApp.Id, default).Returns(authDetails);
        _onboardingApi.GetDecisionStepsByQuery(Arg.Is<DecisionEngineStepQueryDto>(x => x.CompanyId == CompanyId), default).Returns(decisionEngineSteps);
        _companyApi.GetBankStatements(companyModel.Id, default).Returns(bankStatements);
        _mapper.Map<List<BankStatementDocument>>(bankStatements).Returns(new List<BankStatementDocument>());

        var expectedResult = new AccountDetailedModel()
        {
            Id = companyModel.Id,
            CompanyId = companyModel.Id,
            EinHash = accountAuth.EinHash,
            BusinessName = companyModel.LegalName,
            AccountStatus = companyModel.AccountStatus ?? AccountStatusEnum.New,
            LastValidatedAt = _currentDateTime,
            AccountStatusText = companyModel.StatusEvent,
            Product = CreditApplicationType.LineOfCredit,
            BankCreditStatus = new()
            {
                BusinessOutstandingBalance = targetCredit.CreditDetails.OutstandingCredit,
                AnnualRevenue = accountAuth.BusinessDetails.AnnualRevenue ?? 0,
                CompanyIncome = accountAuth.BusinessDetails.CompanyIncome,
                TotalAcceptableDebtAmount = accountAuth.BusinessDetails.TotalAcceptableDebtAmount,
            },
            CreditDetails = new AccountCreditDetailsModel
            {
                Status = CreditStatus.OnHold,
                Id = targetCredit.Id,
                PurchaseType = Enum.Parse<CreditPurchaseType>(targetCredit.PurchaseType.ToString()!, true),
                CreditLimit = targetCredit.CreditLimit,
                OutstandingBalance = targetCredit.CreditDetails.OutstandingCredit,
                CurrentBalance = targetCredit.CreditDetails.OutstandingCredit,
                AvailableBalance = targetCredit.CreditDetails.AvailableCredit,
                NumberOfDraws = targetCredit.CreditDetails.NumberOfDraws,
                RevenueFallPercentage = 80,
                TotalProcessingAmount = 100,
                TotalPastDueAmount = 55,
                NumberOfPastDueDraws = 1,
                AmountOfActiveDraws = 1000,
                NumberOfActiveDraws = 2
            },
            BankAccounts = new List<BankAccountDetails>()
            {
                new()
                {
                    Identifier = bankAccountId,
                    Name = expectedBankAccountName,
                    ConnectionType = BankAccountConnectionType.Plaid,
                    IncludeInCashFlow = true,
                    PlaidConnectionStatus = PlaidConnectionStatus.Active,
                    IsCheckingAccount = new()
                    {
                        Result = AutomatedDecisionType.Pass,
                        Value = true
                    },
                    BusinessNameVerification = AutomatedDecisionType.Pass,
                    RoutingNumber = "number",
                    AccountHolderName = "name",
                    AccountNumber = "********1234"
                }
            },
            BankStatements = new List<BankStatementDocument>(),
            ManuallyUploadedDocuments = new List<UploadedDocument>(),
            DocumentApprovals = new List<DocumentApproval>()
            {
                new()
                {
                    Id = documentId,
                    DocumentUrl = "source:/url/document.docx",
                    DocumentName = "document.docx",
                    SignerName = "FirstName LastName",
                    PercentOwned = 2,
                    IpAddress = "IpAddress",
                    ApprovalDate = new DateOnly(_currentDateTime.Year, _currentDateTime.Month, _currentDateTime.Day),
                    IsApproved = true,
                    OwnerIdentifier = "Owner",
                    TemplateValidFromDate = new DateTime()
                }
            },
            IhcOverAllDetails = new AccountIhcOverAllDetailsModel
            {
                ActiveCredits = 2,
                PastDueCredits = 1,
                NumberOfInvoices = 4,
                AmountPaid = 200,
                NumberOfUnpaidInvoices = 1,
                NumberOfDueInvoices = 1,
                NumberOfPastDueInvoices = 1,
                AmountOfDue = 400,
                AmountOfPastDue = 300,
                CalendarDaysLate = 2,
                OldestDueOrPastDueDate = DateOnly.FromDateTime(_currentDateTime).AddDays(-2)
            },
            CustomerSettings = new CustomerSettingsModel() { DebtInvestorTradeCredit = DebtInvestorType.Aion },
        };

        var result = await _accountService.GetDetailedAccount(companyModel.Id, merchantId, uiProductType, default);
        result.ShouldBeEquivalentTo(expectedResult);

        await _loanApi.Received(1).GetCreditsByFilters(Arg.Any<CreditFilterDto>(), default);
        await _companyApi.Received(1).GetBankAccountsByCompanyId(companyModel.Id, default);
        await _companyApi.Received(1).GetCustomersByMerchantIdAndCompanyId(merchantId, companyModel.Id, default);
        await _companyApi.Received(1).GetCompanyById(companyModel.Id, default);
        await _loanApi.Received(1).GetLoansByQuery(Arg.Any<LoanQueryDto>(), default);
        await _onboardingApi.Received(1).GetAccountAuthorizationByCompanyId(companyModel.Id, default);
        await _companyApi.Received(1).GetDocuments(companyModel.Id, default);
        await _onboardingApi.Received(1).GetCreditApplicationsList(Arg.Any<CreditApplicationQueryDto>(), default);
        await _onboardingApi.Received(1).GetApplicationAuthorizationDetails(targetCreditApp.Id, default);
        await _onboardingApi.Received(1).GetDecisionStepsByQuery(Arg.Any<DecisionEngineStepQueryDto>(), default);
        await _companyApi.Received(1).GetBankStatements(companyModel.Id, default);
    }

    [Fact]
    public Task GetDetailedAccount_CompanyWasNotFound_ShouldThrowException()
    {
        _companyApi.GetCompanyById(CompanyId, default)!.Returns((CompanyModel?)null);

        var act = async () => await _accountService.GetDetailedAccount(CompanyId, null, UICreditApplicationType.loc, default);

        return act.ShouldThrowAsync<InvalidOperationException>();
    }

    [Fact]
    public async Task GetDetailedAccount_CompanyFound_ReturnsEmptyDetailedAccount()
    {
        var company = new CompanyModel()
        {
            Id = CompanyId,
            Name = "Name",
            LegalName = "Legal Name"
        };

        var merchantId = nameof(CreditDto.MerchantId);
        var productType = ProductType.LineOfCredit;
        var bankStatements = new List<BankStatementResponse>();

        _companyApi.GetCompanyById(company.Id, default)!.Returns(company);
        _loanApi.GetCreditsByFilters(Arg.Is<CreditFilterDto>(x => x.CompanyId == company.Id && x.MerchantId == merchantId && x.Product == productType), default).Returns(new List<CreditDto?>());
        _companyApi.GetBankAccountsByCompanyId(company.Id, default).Returns(new List<BankAccountModel>());
        _companyApi.GetCustomersByMerchantIdAndCompanyId(merchantId, company.Id, default).Returns(new List<CustomerModel>());
        _onboardingApi.GetAccountAuthorizationByCompanyId(company.Id, default).Returns((AccountAuthorizationDto?)null);
        _companyApi.GetDocuments(company.Id, default).Returns(new List<DocumentLoanApplicationResponse>());
        _onboardingApi.GetCreditApplicationsList(Arg.Is<CreditApplicationQueryPaginatedDto>(x => x.CompanyId == CompanyId && x.MerchantId == merchantId && x.Type!.SequenceEqual(new[]
        {
            CreditApplicationType.LineOfCredit.ToString()
        })), default).Returns(Enumerable.Empty<CreditApplicationDto>());
        _onboardingApi.GetApplicationAuthorizationDetails(company.Id, default).Returns((CreditApplicationAuthorizationDetailsDto?)null);
        _companyApi.GetBankStatements(company.Id, default).Returns(bankStatements);
        _mapper.Map<List<BankStatementDocument>>(bankStatements).Returns(new List<BankStatementDocument>());

        var result = await _accountService.GetDetailedAccount(CompanyId, merchantId, UICreditApplicationType.loc, default);

        result.ShouldBeEquivalentTo(new AccountDetailedModel
        {
            Id = company.Id,
            CompanyId = company.Id,
            EinHash = string.Empty,
            BusinessName = company.LegalName,
            AccountStatus = AccountStatusEnum.New,
            AccountStatusText = string.Empty,
            LastValidatedAt = DateTime.MinValue,
            CreditDetails = new AccountCreditDetailsModel(),
            BankCreditStatus = new(),
            BankAccounts = new List<BankAccountDetails>(),
            ManuallyUploadedDocuments = new List<UploadedDocument>(),
            DocumentApprovals = new List<DocumentApproval>(),
            BankStatements = new List<BankStatementDocument>(),
            CustomerSettings = new CustomerSettingsModel
            {
                DebtInvestorTradeCredit = DebtInvestorType.Arcadia,
                CalculatedDebtInvestorTradeCredit = DebtInvestorType.Arcadia,
                InHouseCredit = new CustomerInHouseCreditModel
                {
                    CalculatedDebtInvestor = DebtInvestorType.Arcadia
                }
            },
        });

        await _loanApi.GetCreditsByFilters(Arg.Is<CreditFilterDto>(x => x.CompanyId == company.Id && x.MerchantId == merchantId && x.Product == productType), default);
        await _companyApi.Received(1).GetBankAccountsByCompanyId(company.Id, default);
        await _companyApi.Received(1).GetCompanyById(company.Id, default);
        await _companyApi.Received(1).GetCustomersByMerchantIdAndCompanyId(merchantId, company.Id, default);
        await _onboardingApi.Received(1).GetAccountAuthorizationByCompanyId(company.Id, default);
        await _companyApi.Received(1).GetDocuments(company.Id, default);
        await _companyApi.Received(1).GetBankStatements(company.Id, default);
        await _onboardingApi.Received(1).GetCreditApplicationsList(Arg.Any<CreditApplicationQueryDto>(), default);
    }

    [Theory, AutoDataWithDateOnly]
    public async Task GetApprovedAccounts_WithCreditAppFilters_ReturnsPaginatedFilteredAccounts(
        ReadOnlyCollection<CompanyModel> companies,
        ReadOnlyCollection<CreditApplicationDto> applications)
    {
        // Arrange
        var filter = _fixture.Build<ApprovedAccountsFilter>()
            .With(f => f.PageSize, 2)
            .With(f => f.PageNumber, 1)
            .With(f => f.ProductType, [CreditApplicationType.LineOfCredit])
            .With(f => f.Category, [BusinessCategoryType.DealerRetailerSupplier])
            .Create();

        var paginatedCompanies = new PaginatedCompanyResponse
        {
            Result = companies.Take(4).ToList(),
            Total = 4,
            Offset = 1,
            Count = 2
        };

        var creditApplications = applications
            .Take(2)
            .Select(x => new CreditApplicationDto
            {
                Id = x.Id,
                CompanyId = paginatedCompanies.Result[0].Id,
                Status = nameof(OBS.Enums.CreditApplicationStatus.Approved),
                // Copy other necessary properties from x
                Type = x.Type,
                BusinessName = x.BusinessName,
                MerchantId = x.MerchantId,
                ApplicationDate = x.ApplicationDate,
                CreditDetails = x.CreditDetails
            })
            .ToList();

        _mapper.Map<CompanyQueryPaginated>(filter).Returns(new CompanyQueryPaginated
        {
            PageSize = int.MaxValue,
            PageNumber = 1
        });

        _companyApi.GetCompaniesList(
                Arg.Is<CompanyQueryPaginated>(x => x.PageSize == int.MaxValue && x.PageNumber == 1),
                Arg.Any<CancellationToken>())
            .Returns(paginatedCompanies);

        _onboardingApi.GetCreditApplicationsByCompanyIds(
                Arg.Any<CompanyIdsDto>(),
                Arg.Is<GetCreditApplicationsByCompanyIdsQueryDto>(x =>
                    x.Status!.Contains(nameof(OBS.Enums.CreditApplicationStatus.Approved)) &&
                    x.Type!.Contains(nameof(CreditApplicationType.LineOfCredit)) &&
                    x.Category!.Contains(nameof(BusinessCategoryType.DealerRetailerSupplier))),
                Arg.Any<CancellationToken>())
            .Returns(creditApplications);

        _loanApi.GetCreditsByCompanyIdArray(
                Arg.Is<string[]>(x => x.Contains(paginatedCompanies.Result[0].Id)),
                false,
                Arg.Any<CancellationToken>())
            .Returns(new List<CreditDto?>());

        // Act
        var result = await _accountService.GetApprovedAccounts(filter, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Result.Count().ShouldBe(1);
        result.TotalCount.ShouldBe(1);
        result.PageNumber.ShouldBe(1);

        await _companyApi.Received(1).GetCompaniesList(
            Arg.Is<CompanyQueryPaginated>(x => x.PageSize == int.MaxValue && x.PageNumber == 1),
            Arg.Any<CancellationToken>());

        await _onboardingApi.Received(1).GetCreditApplicationsByCompanyIds(
            Arg.Any<CompanyIdsDto>(),
            Arg.Is<GetCreditApplicationsByCompanyIdsQueryDto>(x =>
                x.Status!.Contains(nameof(OBS.Enums.CreditApplicationStatus.Approved)) &&
                x.Type!.Contains(nameof(CreditApplicationType.LineOfCredit)) &&
                x.Category!.Contains(nameof(BusinessCategoryType.DealerRetailerSupplier))),
            Arg.Any<CancellationToken>());

        await _loanApi.Received(1).GetCreditsByCompanyIdArray(
            Arg.Is<string[]>(x => x.Contains(paginatedCompanies.Result[0].Id)),
            false,
            Arg.Any<CancellationToken>());
    }

    [Fact]
    public async Task GetDetailedAccount_WithPastDueDate_ShouldCalculateCalendarDaysLate()
    {
        // Arrange
        var calendarDaysLate = 15; // This should match the days between currentDate and pastDueDate
        var companyId = "company123";
        var merchantId = "merchant123";
        var pastDueDate = _currentDateOnly.AddDays(-calendarDaysLate);

        var company = new CompanyModel { Id = companyId, LegalName = "Test Company" };
        var credit = new CreditDto
        {
            Id = Guid.NewGuid(),
            Product = ProductType.LineOfCredit,
            MerchantId = merchantId,
            Status = CreditStatus.Active,
            CreditDetails = new CreditDetailsDto
            {
                OldestDueOrPastDueDate = pastDueDate,
            }
        };

        _companyApi.GetCompanyById(companyId, CancellationToken.None).Returns(company);
        _loanApi.GetCreditsByFilters(Arg.Any<CreditFilterDto>(), CancellationToken.None).Returns(new List<CreditDto> { credit }!);
        _companyApi.GetBankAccountsByCompanyId(companyId, CancellationToken.None).Returns(new List<BankAccountModel>());
        _companyApi.GetCustomersByMerchantIdAndCompanyId(merchantId, companyId, CancellationToken.None).Returns(new List<CustomerModel>());
        _onboardingApi.GetAccountAuthorizationByCompanyId(companyId, CancellationToken.None).Returns((AccountAuthorizationDto)null!);
        _companyApi.GetDocuments(companyId, CancellationToken.None).Returns(new List<DocumentLoanApplicationResponse>());
        _onboardingApi.GetCreditApplicationsList(Arg.Any<CreditApplicationQueryPaginatedDto>(), CancellationToken.None)
            .Returns(new List<CreditApplicationDto>());
        _companyApi.GetBankStatements(companyId, CancellationToken.None).Returns(new List<BankStatementResponse>());
        _mapper.Map<List<BankStatementDocument>>(Arg.Any<List<BankStatementResponse>>())
            .Returns(new List<BankStatementDocument>());

        // Act
        var result = await _accountService.GetDetailedAccount(companyId, merchantId, UICreditApplicationType.loc, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.CreditDetails.CalendarDaysLate.ShouldBe(calendarDaysLate);
        result.CreditDetails.OldestDueOrPastDueDate.ShouldBe(pastDueDate);
    }
}
