using BlueTape.BackOffice.DecisionEngine.DataAccess.External.Constants;
using BlueTape.BackOffice.DecisionEngine.DataAccess.External.DI.Constants;
using BlueTape.BackOffice.DecisionEngine.DataAccess.External.PaymentApi.HttpClients;
using BlueTape.BackOffice.DecisionEngine.DataAccess.External.PaymentApi.Proxy;
using BlueTape.BackOffice.DecisionEngine.Domain.Models;
using BlueTape.Integrations.Aion.Infrastructure.Enums;
using BlueTape.Integrations.Aion.Transactions;
using BlueTape.OBS.DTOs;
using BlueTape.PaymentService.Enums;
using BlueTape.PaymentService.Models;
using BlueTape.Utilities.Models;
using Microsoft.Extensions.Configuration;
using MongoDB.Bson;
using NSubstitute;

namespace BlueTape.BackOffice.DecisionEngine.DataAccess.External.Tests.PaymentApi;

public class PaymentApiProxyTests
{
    private readonly PaymentApiProxy _paymentApiProxy;
    private readonly IPaymentHttpClient _paymentHttpClient = Substitute.For<IPaymentHttpClient>();
    private readonly IConfiguration _configuration = Substitute.For<IConfiguration>();
    private readonly DateOnly _currentDateOnly = DateOnly.FromDateTime(DateTime.UtcNow);

    public PaymentApiProxyTests()
    {
        _paymentApiProxy = new PaymentApiProxy(_paymentHttpClient, _configuration);
    }

    [Fact]
    public async Task ApprovePaymentRequest_ValidRequest_ReturnsValidResponse()
    {
        var id = new Guid();
        var createdBy = ObjectId.GenerateNewId().ToString();

        _paymentHttpClient.PatchAsync<object>(Arg.Any<string>(), Arg.Any<object>(), Arg.Any<IReadOnlyDictionary<string, string>>(), Arg.Any<CancellationToken>())!
            .Returns(Task.FromResult<object>(null!));

        var exception = await Record.ExceptionAsync(() => _paymentApiProxy.ApprovePaymentRequest(id, new ApprovePaymentRequestModel(), createdBy, default));

        Assert.Null(exception);
    }

    [Fact]
    public async Task GetById_ValidId_ReturnsExpectedPaymentRequestModel()
    {
        var id = Guid.NewGuid();
        var expected = new PaymentRequestModel
        {
            Id = id,
        };

        _paymentHttpClient
            .Get<PaymentRequestModel>(Arg.Is<string>(url => url.Contains(id.ToString())), Arg.Any<CancellationToken>())
            .Returns(expected);

        var result = await _paymentApiProxy.GetById(id, CancellationToken.None);

        Assert.NotNull(result);
        Assert.Equal(expected.Id, result.Id);
    }

    [Fact]
    public async Task GetAccounts_ValidRequest_ReturnsValidResponse()
    {
        var paymentProvider = "TestProvider";

        var fundingId = "testFundingId";
        var collectionId = "testCollectionId";

        _configuration[KeyVaultKeys.FundingAccountId] = fundingId;
        _configuration[KeyVaultKeys.CollectionAccountId] = collectionId;

        var aionAccounts = new List<AionAccount>
        {
            new(){ Id = fundingId, AvailableBalance = 100, Name = "Funding Account", AmountOnHold = 10 },
            new() { Id = collectionId, AvailableBalance = 200, Name = "Collection Account", AmountOnHold = 20 }
        };

        _paymentHttpClient.Get<List<AionAccount>>(Arg.Any<string>(), Arg.Any<CancellationToken>())
            .Returns(aionAccounts);

        var result = await _paymentApiProxy.GetAccounts(paymentProvider, CancellationToken.None);

        Assert.NotNull(result);
        Assert.Equal(2, result.Count);

        var fundingAccount = result.FirstOrDefault(x => x.Type == "Funding");
        var collectionAccount = result.FirstOrDefault(x => x.Type == "Collection");

        Assert.NotNull(fundingAccount);
        Assert.Equal("Funding Account", fundingAccount.Name);
        Assert.Equal(100, fundingAccount.AvailableBalance);
        Assert.Equal(10, fundingAccount.AmountOnHold);

        Assert.NotNull(collectionAccount);
        Assert.Equal("Collection Account", collectionAccount.Name);
        Assert.Equal(200, collectionAccount.AvailableBalance);
        Assert.Equal(20, collectionAccount.AmountOnHold);
    }

    [Fact]
    public async Task GetDisbursements_ValidRequest_ReturnsTransformedResults()
    {
        var filterQuery = new PaymentRequestFilterQuery
        {
            PageNumber = 1,
            PageSize = 10,
            SortOrder = "desc",
            FlowTemplateCodes = new List<string>() { "CREATE.PAYNOW.INVOICE_PAYMENT" },
            Status = new List<string>() { "Processed" },
            IsConfirmed = false,
            ApprovedFrom = _currentDateOnly.AddDays(-5),
            ApprovedTo = _currentDateOnly.AddDays(5)
        };
        var paymentRequestModel = new PaymentRequestModel
        {
            PayerId = Guid.NewGuid().ToString(),
            SellerId = Guid.NewGuid().ToString(),
            Status = PaymentRequestStatus.Processed,
            Transactions = new List<PaymentTransactionModel>() { new() }
        };

        var expectedResult = new GetQueryWithPaginationResultDto<PaymentRequestModel>
        {
            Result = new List<PaymentRequestModel> { paymentRequestModel }
        };
        _paymentHttpClient.Get<GetQueryWithPaginationResultDto<PaymentRequestModel>>(Arg.Any<string>(), Arg.Any<CancellationToken>())
            .Returns(expectedResult);

        var result = await _paymentApiProxy.GetPaymentRequests(filterQuery, CancellationToken.None);

        Assert.NotNull(result);
        Assert.NotEmpty(result.Result);
        Assert.Equal(expectedResult.Result.First().Id, result.Result.First().Id);
    }

    [Fact]
    public async Task GetAccountTransactions_ShouldCallHttpClientWithCorrectParameters()
    {
        var request = new TransactionsQuery
        {
            PageNumber = 1,
            PageSize = 10,
            AccountCodeType = AccountCodeType.LOCKBOXCOLLECTION
        };

        var expectedResponse = new PaginatedResponse<TransactionListObj>
        {
            Result = new List<TransactionListObj> { new() },
            Total = 1
        };

        _paymentHttpClient
            .Get<PaginatedResponse<TransactionListObj>>(
                Arg.Is<string>(url =>
                    url.StartsWith($"{PaymentServiceConstants.Transactions}/accountCode") &&
                    url.Contains("pageNumber=1") &&
                    url.Contains("pageSize=10") &&
                    url.Contains($"accountCodeType={AccountCodeType.LOCKBOXCOLLECTION.ToString()}")),
                Arg.Any<CancellationToken>())
            .Returns(expectedResponse);

        var result = await _paymentApiProxy.GetAccountTransactions(request, CancellationToken.None);

        Assert.NotNull(result);
        Assert.Single(result.Result);
        await _paymentHttpClient.Received(1)
            .Get<PaginatedResponse<TransactionListObj>>(
                Arg.Any<string>(),
                Arg.Any<CancellationToken>());
    }
}
